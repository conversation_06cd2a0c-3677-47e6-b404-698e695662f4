# Local Development Setup

This guide will help you set up the Workwell application for local development using Docker containers.

## Prerequisites

- Docker Desktop installed and running
- Node.js 18+ installed
- Git

## Quick Start

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd workwell
   ```

2. **Run the setup script**:
   ```bash
   ./scripts/dev-setup.sh
   ```

3. **Start the development server**:
   ```bash
   npm run dev
   ```

4. **Open your browser** to [http://localhost:3000](http://localhost:3000)

## Manual Setup (Alternative)

If you prefer to set up manually:

### 1. Start Docker Services

```bash
docker-compose up -d
```

This will start:
- PostgreSQL database on port 5432
- Redis for sessions on port 6379
- MailHog for email testing on port 8025
- Adminer for database management on port 8080

### 2. Install Dependencies

```bash
npm install
```

### 3. Start Development Server

```bash
npm run dev
```

## Services Overview

### PostgreSQL Database
- **Host**: localhost:5432
- **Database**: workwell
- **Username**: workwell_user
- **Password**: workwell_password

The database is automatically initialized with:
- Complete schema (tables, indexes, triggers)
- Sample data (companies, benefits, users)
- Test user accounts

### Redis
- **Host**: localhost:6379
- Used for session storage and caching

### MailHog (Email Testing)
- **Web UI**: http://localhost:8025
- **SMTP**: localhost:1025
- Captures all emails sent by the application

### Adminer (Database Management)
- **URL**: http://localhost:8080
- **System**: PostgreSQL
- **Server**: postgres
- **Username**: workwell_user
- **Password**: workwell_password
- **Database**: workwell

## Test Accounts

The following test accounts are pre-created (password: `password123`):

| Email | Company | Role |
|-------|---------|------|
| <EMAIL> | SAP | Company Representative |
| <EMAIL> | Deutsche Bank | Company Representative |
| <EMAIL> | Accenture | Company Representative |
| <EMAIL> | TechStart GmbH | Company Representative |
| <EMAIL> | None | General User |

## Development Features

### Local Authentication
- Replaces Clerk for development
- Simple email/password authentication
- Session-based authentication with cookies
- User management API routes

### Database
- PostgreSQL with sample data
- Companies from Frankfurt with benefits
- Benefit verification system ready for testing

### Email Testing
- All emails are captured by MailHog
- No external email service needed
- View emails at http://localhost:8025

## Common Tasks

### View Database
1. Open http://localhost:8080
2. Login with PostgreSQL credentials
3. Explore tables and data

### Test Email Functionality
1. Trigger an email in the app
2. Check http://localhost:8025 to see the email

### Reset Database
```bash
docker-compose down -v
docker-compose up -d
```

### View Logs
```bash
# All services
docker-compose logs

# Specific service
docker-compose logs postgres
docker-compose logs redis
```

### Stop Services
```bash
docker-compose down
```

### Stop and Remove All Data
```bash
docker-compose down -v
```

## Development Workflow

1. **Make code changes** - The development server will auto-reload
2. **Test authentication** - Use the pre-created test accounts
3. **Test company features** - Sign in with company email addresses
4. **Test benefit verification** - Use different user accounts
5. **Check emails** - View captured emails in MailHog
6. **Inspect database** - Use Adminer to view data changes

## Troubleshooting

### Port Conflicts
If you get port conflicts, you can modify the ports in `docker-compose.yml`:
```yaml
ports:
  - "5433:5432"  # Change PostgreSQL port
  - "6380:6379"  # Change Redis port
```

### Database Connection Issues
1. Ensure PostgreSQL container is running: `docker ps`
2. Check logs: `docker-compose logs postgres`
3. Restart services: `docker-compose restart`

### Authentication Issues
1. Clear browser cookies
2. Check session in Redis: `docker exec workwell-redis redis-cli keys "*"`
3. Restart the development server

### Performance Issues
1. Ensure Docker has enough resources allocated
2. Check Docker Desktop settings
3. Consider using Docker Desktop with WSL2 on Windows

## Environment Variables

The application uses these environment variables for local development:

```env
# Local Development Database
DATABASE_URL=postgresql://workwell_user:workwell_password@localhost:5432/workwell

# Local Authentication
USE_LOCAL_AUTH=true
SESSION_SECRET=your_local_session_secret

# Redis
REDIS_URL=redis://localhost:6379

# Email (MailHog)
SMTP_HOST=localhost
SMTP_PORT=1025

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development
```

## Production Differences

When deploying to production (Kubernetes):
1. Keep `USE_LOCAL_AUTH=true` (self-hosted authentication)
2. Use production PostgreSQL database
3. Use production Redis instance
4. Configure production email service (SMTP)
5. Set proper resource limits and scaling
6. Configure ingress and SSL certificates

## Contributing

1. Make your changes
2. Test with local setup
3. Ensure all features work with test accounts
4. Submit pull request

## Support

If you encounter issues:
1. Check this documentation
2. Review Docker logs
3. Ensure all prerequisites are met
4. Check GitHub issues for similar problems
