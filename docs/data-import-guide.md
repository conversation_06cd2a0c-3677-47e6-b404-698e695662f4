# WorkWell Data Import Guide

This guide explains how to import companies and benefits data into WorkWell using the generic import script.

## Overview

The WorkWell data import system supports importing companies and employee benefits from CSV and JSON files with flexible field mapping and validation. The system is designed to be generic and configurable, allowing you to import data from various sources with different field names and formats.

## Quick Start

### Import Demo Data

```bash
# Import demo companies (CSV format)
npm run import:demo-companies

# Import demo benefits (JSON format)  
npm run import:demo-benefits

# Import demo benefits (CSV format)
npm run import:demo-benefits-csv

# Dry run (preview without importing)
npm run import:demo-companies:dry-run
npm run import:demo-benefits:dry-run
```

### Import Custom Data

```bash
# Import companies from custom CSV file
node scripts/import-data.js --type=companies --file=path/to/companies.csv

# Import benefits from custom JSON file
node scripts/import-data.js --type=benefits --file=path/to/benefits.json

# Use custom field mappings
node scripts/import-data.js --type=companies --file=data.csv --mapping=mappings.json
```

## Command Line Options

| Option | Description | Required | Example |
|--------|-------------|----------|---------|
| `--type` | Data type to import (`companies` or `benefits`) | Yes | `--type=companies` |
| `--file` | Path to data file (CSV or JSON) | Yes | `--file=data/companies.csv` |
| `--format` | File format (`csv` or `json`) | No* | `--format=csv` |
| `--mapping` | Path to custom field mapping file | No | `--mapping=custom.json` |
| `--dry-run` | Preview changes without importing | No | `--dry-run` |
| `--category` | Filter benefits by category | No | `--category=health` |
| `--help` | Show help message | No | `--help` |

*Format is auto-detected from file extension if not specified.

## File Formats

### CSV Format

CSV files must include a header row with column names. The import script will automatically map common field names to the database schema.

**Example companies.csv:**
```csv
name,location,size,industry,description,domain,career_url,verified
TechStart GmbH,Berlin Germany,startup,Technology,AI startup,techstart.de,https://techstart.de/careers,false
GreenEnergy Solutions,Munich Germany,medium,Energy,Renewable energy,greenenergy.de,https://greenenergy.de/jobs,true
```

**Example benefits.csv:**
```csv
Benefit Name,Type,Emoji,Details
Health Insurance,health,🏥,Comprehensive health coverage
Flexible Hours,work_life,⏰,Flexible working schedule
```

### JSON Format

JSON files should contain an array of objects or a single object (which will be treated as an array with one item).

**Example companies.json:**
```json
[
  {
    "name": "TechStart GmbH",
    "location": "Berlin, Germany",
    "size": "startup",
    "industry": "Technology",
    "description": "Innovative AI startup",
    "domain": "techstart.de",
    "career_url": "https://techstart.de/careers",
    "verified": false
  }
]
```

**Example benefits.json:**
```json
[
  {
    "name": "Health Insurance Premium",
    "category": "health",
    "icon": "🏥",
    "description": "Comprehensive health insurance coverage"
  }
]
```

## Field Mappings

The import script uses flexible field mapping to handle different column names and formats. It will automatically try to match common variations of field names.

### Default Company Field Mappings

| Database Field | Possible Source Fields |
|----------------|------------------------|
| `name` | name, company_name, Company Name, Name |
| `location` | location, Location, city, City |
| `size` | size, Size, company_size, Company Size |
| `industry` | industry, Industry, sector, Sector |
| `description` | description, Description, about, About |
| `domain` | domain, Domain, website, Website |
| `career_url` | career_url, career_page, CareerURL, Career URL, careers |
| `verified` | verified, Verified, is_verified |

### Default Benefit Field Mappings

| Database Field | Possible Source Fields |
|----------------|------------------------|
| `name` | name, benefit_name, Benefit Name, Name |
| `category` | category, Category, type, Type |
| `icon` | icon, Icon, emoji, Emoji |
| `description` | description, Description, details, Details |

### Custom Field Mappings

You can create a custom mapping file to handle non-standard field names:

**custom-mappings.json:**
```json
{
  "companies": {
    "name": ["organization", "firm", "company"],
    "location": ["headquarters", "office_location"],
    "industry": ["business_type", "field"]
  },
  "benefits": {
    "name": ["benefit_title", "title"],
    "category": ["benefit_type", "classification"]
  }
}
```

Use with: `--mapping=custom-mappings.json`

## Data Validation

### Company Validation Rules

- **Name**: Required, non-empty string
- **Location**: Required, non-empty string  
- **Industry**: Required, non-empty string
- **Size**: Must be one of: `startup`, `small`, `medium`, `large`, `enterprise`
- **Domain**: Must be valid domain format (if provided)
- **Verified**: Boolean value (defaults to false)

### Benefit Validation Rules

- **Name**: Required, non-empty string
- **Category**: Required, must be one of: `health`, `time_off`, `financial`, `development`, `wellness`, `work_life`, `other`
- **Icon**: Optional string (emoji recommended)
- **Description**: Optional text

## Data Transformation

The import script automatically applies transformations to ensure data consistency:

### Company Transformations

- **Domain**: Removes protocol (http/https) and trailing slashes, converts to lowercase
- **Size**: Normalizes to lowercase, defaults to "medium" if invalid
- **Verified**: Converts string "true"/"false" to boolean

### Benefit Transformations

- **Category**: Normalizes to lowercase, defaults to "other" if invalid
- **Name/Description**: Trims whitespace

## Duplicate Handling

The import script automatically detects and skips duplicates:

- **Companies**: Matched by name (case-insensitive) or domain
- **Benefits**: Matched by name (case-insensitive)

Duplicates are logged and counted in the import statistics.

## Error Handling

The script provides comprehensive error handling and logging:

- **Validation Errors**: Invalid data is skipped with detailed error messages
- **File Errors**: Missing files or parsing errors are reported
- **Database Errors**: Connection and query errors are handled gracefully
- **Statistics**: Complete import statistics are provided at the end

## Examples

### Basic Import Examples

```bash
# Import companies from CSV
npm run import:demo-companies

# Import benefits from JSON with dry run
npm run import:demo-benefits:dry-run

# Import only health benefits
node scripts/import-data.js --type=benefits --file=data/benefits.json --category=health
```

### Advanced Import Examples

```bash
# Import with custom mappings
node scripts/import-data.js --type=companies --file=custom-companies.csv --mapping=custom-mappings.json

# Import JSON benefits with dry run
node scripts/import-data.js --type=benefits --file=benefits.json --dry-run

# Import specific benefit category
node scripts/import-data.js --type=benefits --file=all-benefits.csv --category=wellness --dry-run
```

## Troubleshooting

### Common Issues

1. **File not found**: Ensure the file path is correct and relative to the project root
2. **Invalid format**: Check that CSV files have headers and JSON files are valid
3. **Validation errors**: Review the error messages and fix data format issues
4. **Database connection**: Ensure PostgreSQL is running and environment variables are set

### Environment Variables

Make sure these environment variables are set:

```bash
DB_USER=workwell
DB_HOST=localhost  
DB_NAME=workwell
DB_PASSWORD=workwell
DB_PORT=5432
```

### Getting Help

```bash
# Show detailed help
node scripts/import-data.js --help

# Test with dry run first
node scripts/import-data.js --type=companies --file=your-file.csv --dry-run
```

## Demo Data

The repository includes demo data files in the `data/` directory:

- `demo-companies.csv`: Sample company data in CSV format
- `demo-benefits.json`: Sample benefit data in JSON format  
- `demo-benefits.csv`: Sample benefit data in CSV format
- `custom-mappings.json`: Example custom field mappings

These files demonstrate the expected format and can be used for testing the import functionality.
