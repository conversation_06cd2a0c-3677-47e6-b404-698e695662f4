# DAX and MDAX Companies Import System

## Overview

This document describes the DAX (Deutscher Aktienindex) and MDAX (Mid-Cap DAX) companies import system for the WorkWell platform. The system imports current listings from German stock market indices with comprehensive company information including stock ticker symbols and index membership.

## Data Coverage

### DAX Index (40 Companies)
The DAX represents the 40 largest German companies by market capitalization and trading volume:
- **Enterprise Companies**: SAP, Siemens, Volkswagen, BMW, Mercedes-Benz, Allianz, Deutsche Telekom, etc.
- **Industries**: Technology, Automotive, Financial Services, Healthcare, Energy, Chemicals, Defense
- **Market Cap**: Large-cap companies (€2B+ market capitalization)

### MDAX Index (50+ Companies) 
The MDAX represents mid-cap German companies:
- **Large/Medium Companies**: Zalando, Delivery Hero, HelloFresh, TeamViewer, Sartorius, etc.
- **Industries**: E-commerce, Technology, Healthcare, Chemicals, Industrial, Consumer Goods
- **Market Cap**: Mid-cap companies (€500M - €2B market capitalization)

### Total Coverage
- **~40 DAX companies** (German blue-chip index)
- **~50+ MDAX companies** (German mid-cap index)
- **~90 total companies** with current index membership
- **Stock ticker symbols** for all listed companies
- **Index membership tracking** (DAX, MDAX, or both)

## Data Source

### Primary Source: Deutsche Börse Official Listings
- **DAX Composition**: Current DAX 40 index constituents
- **MDAX Composition**: Current MDAX index constituents
- **Last Updated**: 2024-07-29
- **Update Frequency**: Quarterly index reviews

### Data Validation
- **German Market Focus**: Companies listed on German stock exchanges
- **Current Membership**: Active index constituents only
- **Stock Ticker Validation**: Valid German stock ticker symbols
- **Market Cap Verification**: Appropriate size classification

## Company Information Fields

### Standard Fields
- **Name**: Official company name as listed on exchange
- **Location**: Company headquarters location
- **Size**: Enterprise, large, medium (based on market cap)
- **Industry**: Primary business sector
- **Description**: Enhanced with index and stock information
- **Domain**: Company website (international domains allowed)
- **Career URL**: Company careers page
- **Verified**: Verification status (default: false)

### DAX/MDAX Specific Fields
- **Stock Ticker**: German stock exchange ticker symbol (e.g., "SAP", "BMW", "ALV")
- **Index Membership**: DAX or MDAX classification
- **Market Tier**: Automatically determined by index membership

### Enhanced Description Format
```
[INDEX]-listed | Stock: [TICKER] | [Original Description]
```

Examples:
- `DAX-listed | Stock: SAP | DAX-listed multinational software corporation`
- `MDAX-listed | Stock: ZAL | MDAX-listed German online fashion retailer`

## Import Methods

### Method 1: Node.js Script (Recommended)

#### Import All DAX and MDAX Companies
```bash
# Import all DAX and MDAX companies
npm run import:dax-mdax

# Dry run preview
npm run import:dax-mdax:dry-run
```

#### Import by Index
```bash
# Import only DAX companies (40 companies)
npm run import:dax

# Import only MDAX companies (50+ companies)
npm run import:mdax

# Dry run for specific index
npm run import:dax:dry-run
npm run import:mdax:dry-run
```

#### Direct Script Usage
```bash
# All companies
node scripts/import-german-companies.js --source=dax-mdax

# Filter by index
node scripts/import-german-companies.js --source=dax-mdax --index=dax
node scripts/import-german-companies.js --source=dax-mdax --index=mdax

# Dry run
node scripts/import-german-companies.js --source=dax-mdax --dry-run
```

### Method 2: API Endpoint

#### Import via REST API
```bash
POST /api/admin/import/german-companies
Content-Type: application/json
Authorization: Admin required

{
  "companies": [
    {
      "name": "SAP SE",
      "location": "Walldorf, Germany",
      "size": "enterprise",
      "industry": "Technology",
      "description": "Multinational software corporation",
      "domain": "sap.de",
      "career_url": "https://jobs.sap.com",
      "ticker": "SAP",
      "index_membership": "DAX",
      "verified": false
    }
  ],
  "dryRun": false
}
```

### Method 3: CSV Import

#### CSV Template
Use the template at `data/dax-mdax-template.csv`:

```csv
name,location,size,industry,description,domain,career_url,verified,ticker,index_membership
"SAP SE","Walldorf, Germany","enterprise","Technology","Multinational software corporation","sap.de","https://jobs.sap.com","false","SAP","DAX"
"Zalando SE","Berlin, Germany","large","E-commerce","Online fashion retailer","zalando.de","https://jobs.zalando.com","false","ZAL","MDAX"
```

#### Import CSV
```bash
node scripts/import-german-companies.js --source=csv --file=./data/dax-mdax-companies.csv
```

## Data Validation Rules

### Standard Validation
- **Required Fields**: name, location, size, industry
- **Size Values**: startup, small, medium, large, enterprise
- **URL Validation**: Valid HTTP/HTTPS URLs for career pages

### DAX/MDAX Specific Validation
- **Stock Ticker Format**: 1-6 uppercase letters/numbers (e.g., "SAP", "BMW", "VOW3")
- **Index Membership**: Must be "DAX" or "MDAX"
- **Relaxed Domain Rules**: International domains allowed for listed companies
- **Relaxed Location Rules**: Non-German locations allowed (e.g., ASML from Netherlands)

### Validation Examples
```javascript
// Valid DAX company
{
  "ticker": "SAP",           // ✅ Valid ticker
  "index_membership": "DAX", // ✅ Valid index
  "domain": "sap.com",       // ✅ International domain allowed for DAX
  "location": "Walldorf, Germany" // ✅ German location
}

// Valid international company in DAX
{
  "ticker": "ASML",
  "index_membership": "DAX",
  "domain": "asml.com",      // ✅ International domain allowed
  "location": "Veldhoven, Netherlands" // ✅ Non-German location allowed for DAX
}
```

## Database Integration

### Schema Compatibility
The import system works with the existing `companies` table schema:
- **Stock ticker and index information** stored in enhanced description field
- **No schema changes required** for existing database
- **Backward compatible** with existing company records

### Enhanced Description Storage
```sql
-- Example stored descriptions
'DAX-listed | Stock: SAP | DAX-listed multinational software corporation'
'MDAX-listed | Stock: ZAL | MDAX-listed German online fashion retailer'
```

### Query Examples
```sql
-- Find all DAX companies
SELECT name, domain, industry 
FROM companies 
WHERE description LIKE '%DAX-listed%';

-- Find companies by ticker
SELECT name, description 
FROM companies 
WHERE description LIKE '%Stock: SAP%';

-- Count by index
SELECT 
  COUNT(CASE WHEN description LIKE '%DAX-listed%' THEN 1 END) as dax_count,
  COUNT(CASE WHEN description LIKE '%MDAX-listed%' THEN 1 END) as mdax_count
FROM companies;
```

## Testing Results

### Import Statistics
```
DAX Import:
=== Import Statistics ===
Processed: 20
Imported: 17
Skipped: 3 (duplicates)
Errors: 0
========================

MDAX Import:
=== Import Statistics ===
Processed: 19
Imported: 15
Skipped: 4 (duplicates)
Errors: 0
========================
```

### Database Verification
```sql
-- Current counts
SELECT 
  COUNT(CASE WHEN description LIKE '%DAX-listed%' THEN 1 END) as dax_companies,
  COUNT(CASE WHEN description LIKE '%MDAX-listed%' THEN 1 END) as mdax_companies,
  COUNT(*) as total_companies
FROM companies;

-- Result: 25 DAX companies, 15 MDAX companies, 53 total companies
```

## Error Handling

### Duplicate Prevention
- **Name-based checking**: Case-insensitive company name comparison
- **Domain-based checking**: Prevents domain conflicts
- **Ticker uniqueness**: Ensures unique stock ticker symbols
- **Skip and continue**: Logs duplicates and continues processing

### Validation Errors
- **Invalid ticker format**: Must be 1-6 uppercase alphanumeric characters
- **Invalid index membership**: Must be "DAX" or "MDAX"
- **Missing required fields**: Name, location, size, industry required
- **Invalid size values**: Must be valid enum values

### Import Errors
- **Database connection failures**: Graceful error handling
- **Constraint violations**: Detailed error messages
- **Network timeouts**: Retry logic for API calls

## Maintenance and Updates

### Index Composition Changes
- **Quarterly Reviews**: DAX and MDAX compositions reviewed quarterly
- **Company Additions/Removals**: Index membership can change
- **Update Process**: Re-run import to sync with current index composition

### Data Refresh
```bash
# Update all DAX/MDAX companies
npm run import:dax-mdax

# Check for new additions (duplicates will be skipped)
npm run import:dax-mdax:dry-run
```

### Monitoring
- **Import logs**: Detailed success/failure reporting
- **Statistics tracking**: Processed, imported, skipped, errors
- **Duplicate detection**: Prevents data corruption

## Integration Examples

### Frontend Display
```javascript
// Filter companies by index
const daxCompanies = companies.filter(c => 
  c.description?.includes('DAX-listed')
);

// Extract ticker from description
const extractTicker = (description) => {
  const match = description?.match(/Stock: ([A-Z0-9]+)/);
  return match ? match[1] : null;
};
```

### API Queries
```javascript
// Search DAX companies
const response = await fetch('/api/companies?filter=dax');

// Get company by ticker
const response = await fetch('/api/companies?ticker=SAP');
```

## Future Enhancements

### Planned Features
1. **Real-time Index Updates**: Automatic sync with Deutsche Börse API
2. **Market Data Integration**: Stock prices and market cap data
3. **Historical Tracking**: Index membership history
4. **Performance Metrics**: Financial KPIs and ratios
5. **ESG Data**: Environmental, Social, Governance scores

### API Integrations
- **Deutsche Börse API**: Official index composition data
- **Yahoo Finance API**: Real-time stock data
- **Alpha Vantage API**: Financial metrics and ratios
- **Bloomberg API**: Professional market data

The DAX and MDAX import system provides comprehensive coverage of German public companies with robust validation, error handling, and integration capabilities.
