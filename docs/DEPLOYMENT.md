# Kubernetes Deployment Guide

This guide will help you deploy the Workwell application to your own Kubernetes cluster.

## Prerequisites

1. **Kubernetes Cluster**: A running Kubernetes cluster with kubectl access
2. **Docker Registry**: Access to a Docker registry to store your application images
3. **PostgreSQL Database**: A PostgreSQL instance (can be deployed in the same cluster)
4. **Redis Instance**: A Redis instance for session storage and caching
5. **SMTP Server**: An SMTP server for sending emails (optional for development)

## Step 1: Build and Push Docker Image

1. Create a Dockerfile for the application:
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the application
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

2. Build and push the image:
```bash
docker build -t your-registry/workwell:latest .
docker push your-registry/workwell:latest
```

## Step 2: Deploy PostgreSQL (if not using external database)

Create a PostgreSQL deployment:
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15
        env:
        - name: POSTGRES_DB
          value: workwell
        - name: POSTGRES_USER
          value: workwell_user
        - name: POSTGRES_PASSWORD
          value: workwell_password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
```

## Step 3: Deploy Redis

Create a Redis deployment:
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-storage
          mountPath: /data
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc
```

## Step 4: Deploy the Application

Create the application deployment:
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: workwell
spec:
  replicas: 3
  selector:
    matchLabels:
      app: workwell
  template:
    metadata:
      labels:
        app: workwell
    spec:
      containers:
      - name: workwell
        image: your-registry/workwell:latest
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          value: "**********************************************************/workwell"
        - name: REDIS_URL
          value: "redis://redis:6379"
        - name: USE_LOCAL_AUTH
          value: "true"
        - name: SESSION_SECRET
          value: "your-session-secret"
        - name: NEXT_PUBLIC_APP_URL
          value: "https://your-domain.com"
        - name: NODE_ENV
          value: "production"
```

## Step 5: Create Services

Create services for all components:
```yaml
apiVersion: v1
kind: Service
metadata:
  name: workwell
spec:
  selector:
    app: workwell
  ports:
  - port: 80
    targetPort: 3000
  type: LoadBalancer
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
---
apiVersion: v1
kind: Service
metadata:
  name: redis
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
```

## Step 6: Initialize Database

1. Apply the database schema by running the SQL from `database/schema.sql`
2. Optionally run the seed data from `database/seed.sql`

## Environment Variables Reference

| Variable | Description | Required |
|----------|-------------|----------|
| `DATABASE_URL` | PostgreSQL connection string | Yes |
| `REDIS_URL` | Redis connection string | Yes |
| `USE_LOCAL_AUTH` | Set to "true" for local authentication | Yes |
| `SESSION_SECRET` | Secret for session encryption | Yes |
| `NEXT_PUBLIC_APP_URL` | Your application URL | Yes |
| `NODE_ENV` | Set to "production" | Yes |
| `SMTP_HOST` | SMTP server host (optional) | No |
| `SMTP_PORT` | SMTP server port (optional) | No |
| `SMTP_USER` | SMTP username (optional) | No |
| `SMTP_PASS` | SMTP password (optional) | No |

## Troubleshooting

### Common Issues

1. **Database connection errors**: Verify PostgreSQL service and credentials
2. **Redis connection errors**: Verify Redis service is running
3. **Build errors**: Check for TypeScript errors and missing dependencies

### Logs

- Check pod logs: `kubectl logs -f deployment/workwell`
- Check database logs: `kubectl logs -f deployment/postgres`
- Check Redis logs: `kubectl logs -f deployment/redis`

## Monitoring

Consider setting up:
- Error tracking (Sentry)
- Analytics (PostHog, Plausible)
- Uptime monitoring (Prometheus + Grafana)
- Performance monitoring
- Log aggregation (ELK stack or similar)

## Security Checklist

- [ ] Environment variables are properly set in Kubernetes secrets
- [ ] Database access is restricted to application pods only
- [ ] HTTPS is enabled (via ingress controller)
- [ ] API routes are protected where necessary
- [ ] No sensitive data in client-side code
- [ ] Network policies are configured to restrict pod-to-pod communication
- [ ] Resource limits are set for all containers
- [ ] Security contexts are properly configured
