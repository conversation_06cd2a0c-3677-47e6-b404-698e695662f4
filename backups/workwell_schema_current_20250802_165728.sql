--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13 (Debian 15.13-1.pgdg120+1)
-- Dumped by pg_dump version 15.13 (Debian 15.13-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: cleanup_expired_magic_links(); Type: FUNCTION; Schema: public; Owner: workwell_user
--

CREATE FUNCTION public.cleanup_expired_magic_links() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    DELETE FROM magic_link_tokens WHERE expires_at < NOW();
    DELETE FROM magic_link_rate_limits WHERE window_start < NOW() - INTERVAL '1 hour';
END;
$$;


ALTER FUNCTION public.cleanup_expired_magic_links() OWNER TO workwell_user;

--
-- Name: update_company_analytics_summary(uuid, date); Type: FUNCTION; Schema: public; Owner: workwell_user
--

CREATE FUNCTION public.update_company_analytics_summary(target_company_id uuid, target_date date DEFAULT CURRENT_DATE) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO company_analytics_summary (
        company_id,
        date,
        page_views,
        unique_visitors,
        benefit_interactions,
        search_appearances
    )
    SELECT
        target_company_id,
        target_date,
        COALESCE(views.page_views, 0),
        COALESCE(views.unique_visitors, 0),
        COALESCE(interactions.benefit_interactions, 0),
        COALESCE(appearances.search_appearances, 0)
    FROM (
        SELECT
            COUNT(*) as page_views,
            COUNT(DISTINCT COALESCE(user_id::text, session_id)) as unique_visitors
        FROM company_page_views
        WHERE company_id = target_company_id
        AND DATE(created_at) = target_date
    ) views
    CROSS JOIN (
        SELECT
            COUNT(*) as benefit_interactions
        FROM benefit_search_interactions
        WHERE company_id = target_company_id
        AND DATE(created_at) = target_date
    ) interactions
    CROSS JOIN (
        SELECT
            COUNT(DISTINCT sq.id) as search_appearances
        FROM search_queries sq
        JOIN benefit_search_interactions bsi ON sq.id = bsi.search_query_id
        WHERE bsi.company_id = target_company_id
        AND DATE(sq.created_at) = target_date
    ) appearances
    ON CONFLICT (company_id, date) DO UPDATE SET
        page_views = EXCLUDED.page_views,
        unique_visitors = EXCLUDED.unique_visitors,
        benefit_interactions = EXCLUDED.benefit_interactions,
        search_appearances = EXCLUDED.search_appearances,
        updated_at = NOW();
END;
$$;


ALTER FUNCTION public.update_company_analytics_summary(target_company_id uuid, target_date date) OWNER TO workwell_user;

--
-- Name: update_daily_analytics_summary(date); Type: FUNCTION; Schema: public; Owner: workwell_user
--

CREATE FUNCTION public.update_daily_analytics_summary(target_date date DEFAULT CURRENT_DATE) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO daily_analytics_summary (
        date,
        total_company_views,
        total_searches,
        total_benefit_interactions,
        unique_visitors,
        unique_searchers,
        top_searched_benefits,
        top_viewed_companies
    )
    SELECT
        target_date,
        COALESCE(company_views.total_views, 0),
        COALESCE(searches.total_searches, 0),
        COALESCE(interactions.total_interactions, 0),
        COALESCE(company_views.unique_visitors, 0),
        COALESCE(searches.unique_searchers, 0),
        COALESCE(top_benefits.benefits, '[]'::jsonb),
        COALESCE(top_companies.companies, '[]'::jsonb)
    FROM (
        -- Company views for the day
        SELECT
            COUNT(*) as total_views,
            COUNT(DISTINCT COALESCE(user_id::text, session_id)) as unique_visitors
        FROM company_page_views
        WHERE DATE(created_at) = target_date
    ) company_views
    CROSS JOIN (
        -- Searches for the day
        SELECT
            COUNT(*) as total_searches,
            COUNT(DISTINCT COALESCE(user_id::text, session_id)) as unique_searchers
        FROM search_queries
        WHERE DATE(created_at) = target_date
    ) searches
    CROSS JOIN (
        -- Benefit interactions for the day
        SELECT
            COUNT(*) as total_interactions
        FROM benefit_search_interactions
        WHERE DATE(created_at) = target_date
    ) interactions
    CROSS JOIN (
        -- Top searched benefits
        SELECT
            COALESCE(jsonb_agg(
                jsonb_build_object(
                    'benefit_id', b.id,
                    'benefit_name', b.name,
                    'search_count', benefit_searches.search_count
                )
                ORDER BY benefit_searches.search_count DESC
            ) FILTER (WHERE benefit_searches.search_count > 0), '[]'::jsonb) as benefits
        FROM (
            SELECT
                bsi.benefit_id,
                COUNT(*) as search_count
            FROM benefit_search_interactions bsi
            JOIN search_queries sq ON bsi.search_query_id = sq.id
            WHERE DATE(sq.created_at) = target_date
            GROUP BY bsi.benefit_id
            ORDER BY search_count DESC
            LIMIT 10
        ) benefit_searches
        JOIN benefits b ON benefit_searches.benefit_id = b.id
    ) top_benefits
    CROSS JOIN (
        -- Top viewed companies
        SELECT
            COALESCE(jsonb_agg(
                jsonb_build_object(
                    'company_id', c.id,
                    'company_name', c.name,
                    'view_count', company_views.view_count
                )
                ORDER BY company_views.view_count DESC
            ) FILTER (WHERE company_views.view_count > 0), '[]'::jsonb) as companies
        FROM (
            SELECT
                cpv.company_id,
                COUNT(*) as view_count
            FROM company_page_views cpv
            WHERE DATE(cpv.created_at) = target_date
            GROUP BY cpv.company_id
            ORDER BY view_count DESC
            LIMIT 10
        ) company_views
        JOIN companies c ON company_views.company_id = c.id
    ) top_companies
    ON CONFLICT (date) DO UPDATE SET
        total_company_views = EXCLUDED.total_company_views,
        total_searches = EXCLUDED.total_searches,
        total_benefit_interactions = EXCLUDED.total_benefit_interactions,
        unique_visitors = EXCLUDED.unique_visitors,
        unique_searchers = EXCLUDED.unique_searchers,
        top_searched_benefits = EXCLUDED.top_searched_benefits,
        top_viewed_companies = EXCLUDED.top_viewed_companies,
        updated_at = NOW();
END;
$$;


ALTER FUNCTION public.update_daily_analytics_summary(target_date date) OWNER TO workwell_user;

--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: workwell_user
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_updated_at_column() OWNER TO workwell_user;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: activity_log; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.activity_log (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    event_type character varying(50) NOT NULL,
    event_description text NOT NULL,
    user_id character varying(255),
    user_email character varying(255),
    user_name character varying(255),
    company_id uuid,
    company_name character varying(255),
    benefit_id uuid,
    benefit_name character varying(255),
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT activity_log_event_type_check CHECK (((event_type)::text = ANY ((ARRAY['company_added'::character varying, 'user_registered'::character varying, 'benefit_verified'::character varying, 'benefit_disputed'::character varying, 'benefit_removal_dispute_submitted'::character varying, 'benefit_removal_dispute_approved'::character varying, 'benefit_removal_dispute_rejected'::character varying, 'benefit_removal_dispute_cancelled'::character varying, 'benefit_automatically_removed'::character varying])::text[])))
);


ALTER TABLE public.activity_log OWNER TO workwell_user;

--
-- Name: TABLE activity_log; Type: COMMENT; Schema: public; Owner: workwell_user
--

COMMENT ON TABLE public.activity_log IS 'Tracks all system activities for admin dashboard and audit purposes';


--
-- Name: COLUMN activity_log.event_type; Type: COMMENT; Schema: public; Owner: workwell_user
--

COMMENT ON COLUMN public.activity_log.event_type IS 'Type of event: company_added, user_registered, benefit_verified, benefit_disputed';


--
-- Name: COLUMN activity_log.event_description; Type: COMMENT; Schema: public; Owner: workwell_user
--

COMMENT ON COLUMN public.activity_log.event_description IS 'Human-readable description of the event for display in admin dashboard';


--
-- Name: COLUMN activity_log.metadata; Type: COMMENT; Schema: public; Owner: workwell_user
--

COMMENT ON COLUMN public.activity_log.metadata IS 'Additional event-specific data stored as JSON';


--
-- Name: benefit_categories; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.benefit_categories (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(100) NOT NULL,
    display_name character varying(255) NOT NULL,
    description text,
    icon character varying(50),
    sort_order integer DEFAULT 0,
    is_system boolean DEFAULT false,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.benefit_categories OWNER TO workwell_user;

--
-- Name: benefit_removal_disputes; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.benefit_removal_disputes (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_benefit_id uuid NOT NULL,
    user_id uuid NOT NULL,
    reason text NOT NULL,
    status character varying(50) DEFAULT 'pending'::character varying,
    admin_user_id uuid,
    admin_comment text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT benefit_removal_disputes_status_check CHECK (((status)::text = ANY ((ARRAY['pending'::character varying, 'approved'::character varying, 'rejected'::character varying, 'cancelled'::character varying])::text[])))
);


ALTER TABLE public.benefit_removal_disputes OWNER TO workwell_user;

--
-- Name: benefit_search_interactions; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.benefit_search_interactions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    search_query_id uuid,
    benefit_id uuid NOT NULL,
    company_id uuid NOT NULL,
    interaction_type character varying(50) NOT NULL,
    user_id uuid,
    session_id character varying(255),
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT benefit_search_interactions_interaction_type_check CHECK (((interaction_type)::text = ANY ((ARRAY['view'::character varying, 'click'::character varying, 'verify'::character varying, 'dispute'::character varying])::text[])))
);


ALTER TABLE public.benefit_search_interactions OWNER TO workwell_user;

--
-- Name: benefit_verifications; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.benefit_verifications (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_benefit_id uuid NOT NULL,
    user_id character varying(255) NOT NULL,
    status character varying(50) NOT NULL,
    comment text,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT benefit_verifications_status_check CHECK (((status)::text = ANY ((ARRAY['confirmed'::character varying, 'disputed'::character varying])::text[])))
);


ALTER TABLE public.benefit_verifications OWNER TO workwell_user;

--
-- Name: benefits; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.benefits (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    category character varying(50) NOT NULL,
    icon character varying(255),
    created_at timestamp with time zone DEFAULT now(),
    description text,
    category_id uuid NOT NULL,
    CONSTRAINT benefits_category_check CHECK (((category)::text = ANY ((ARRAY['health'::character varying, 'time_off'::character varying, 'financial'::character varying, 'development'::character varying, 'wellness'::character varying, 'work_life'::character varying, 'other'::character varying])::text[])))
);


ALTER TABLE public.benefits OWNER TO workwell_user;

--
-- Name: companies; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.companies (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    location character varying(255) NOT NULL,
    size character varying(50) NOT NULL,
    industry character varying(255) NOT NULL,
    description text,
    domain character varying(255),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    career_url character varying(500),
    CONSTRAINT companies_size_check CHECK (((size)::text = ANY ((ARRAY['startup'::character varying, 'small'::character varying, 'medium'::character varying, 'large'::character varying, 'enterprise'::character varying])::text[])))
);


ALTER TABLE public.companies OWNER TO workwell_user;

--
-- Name: company_analytics_summary; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.company_analytics_summary (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    date date NOT NULL,
    page_views integer DEFAULT 0,
    unique_visitors integer DEFAULT 0,
    benefit_interactions integer DEFAULT 0,
    search_appearances integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.company_analytics_summary OWNER TO workwell_user;

--
-- Name: company_benefits; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.company_benefits (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    benefit_id uuid NOT NULL,
    added_by character varying(255),
    is_verified boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.company_benefits OWNER TO workwell_user;

--
-- Name: company_page_views; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.company_page_views (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    user_id uuid,
    session_id character varying(255),
    ip_address inet,
    user_agent text,
    referrer text,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.company_page_views OWNER TO workwell_user;

--
-- Name: company_users; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.company_users (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    email character varying(255) NOT NULL,
    is_verified boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.company_users OWNER TO workwell_user;

--
-- Name: company_verification_tokens; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.company_verification_tokens (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    token character varying(255) NOT NULL,
    user_id uuid NOT NULL,
    user_email character varying(255) NOT NULL,
    company_id uuid NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    used_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.company_verification_tokens OWNER TO workwell_user;

--
-- Name: daily_analytics_summary; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.daily_analytics_summary (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    date date NOT NULL,
    total_company_views integer DEFAULT 0,
    total_searches integer DEFAULT 0,
    total_benefit_interactions integer DEFAULT 0,
    unique_visitors integer DEFAULT 0,
    unique_searchers integer DEFAULT 0,
    top_searched_benefits jsonb,
    top_viewed_companies jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.daily_analytics_summary OWNER TO workwell_user;

--
-- Name: magic_link_rate_limits; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.magic_link_rate_limits (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    email character varying(255) NOT NULL,
    request_count integer DEFAULT 1,
    window_start timestamp with time zone DEFAULT now(),
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.magic_link_rate_limits OWNER TO workwell_user;

--
-- Name: magic_link_tokens; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.magic_link_tokens (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    token character varying(255) NOT NULL,
    email character varying(255) NOT NULL,
    user_data jsonb,
    expires_at timestamp with time zone NOT NULL,
    used_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.magic_link_tokens OWNER TO workwell_user;

--
-- Name: migration_log; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.migration_log (
    id integer NOT NULL,
    migration_name character varying(255) NOT NULL,
    description text,
    applied_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.migration_log OWNER TO workwell_user;

--
-- Name: migration_log_id_seq; Type: SEQUENCE; Schema: public; Owner: workwell_user
--

CREATE SEQUENCE public.migration_log_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.migration_log_id_seq OWNER TO workwell_user;

--
-- Name: migration_log_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: workwell_user
--

ALTER SEQUENCE public.migration_log_id_seq OWNED BY public.migration_log.id;


--
-- Name: missing_company_reports; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.missing_company_reports (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_email character varying(255) NOT NULL,
    email_domain character varying(255) NOT NULL,
    first_name character varying(255),
    last_name character varying(255),
    status character varying(50) DEFAULT 'pending'::character varying,
    admin_notes text,
    company_id uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT missing_company_reports_status_check CHECK (((status)::text = ANY ((ARRAY['pending'::character varying, 'reviewed'::character varying, 'added'::character varying, 'rejected'::character varying])::text[])))
);


ALTER TABLE public.missing_company_reports OWNER TO workwell_user;

--
-- Name: saved_companies; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.saved_companies (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id character varying(255) NOT NULL,
    company_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.saved_companies OWNER TO workwell_user;

--
-- Name: search_queries; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.search_queries (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    query_text text NOT NULL,
    user_id uuid,
    session_id character varying(255),
    results_count integer DEFAULT 0,
    filters_applied jsonb,
    ip_address inet,
    user_agent text,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.search_queries OWNER TO workwell_user;

--
-- Name: user_benefit_rankings; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.user_benefit_rankings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    benefit_id uuid NOT NULL,
    ranking integer NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT user_benefit_rankings_ranking_check CHECK (((ranking >= 1) AND (ranking <= 10)))
);


ALTER TABLE public.user_benefit_rankings OWNER TO workwell_user;

--
-- Name: user_sessions; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.user_sessions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    session_token character varying(255) NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.user_sessions OWNER TO workwell_user;

--
-- Name: users; Type: TABLE; Schema: public; Owner: workwell_user
--

CREATE TABLE public.users (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    email character varying(255) NOT NULL,
    first_name character varying(255),
    last_name character varying(255),
    email_verified boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    role character varying(50) DEFAULT 'user'::character varying,
    payment_status character varying(50) DEFAULT 'free'::character varying,
    company_id uuid,
    CONSTRAINT users_payment_status_check CHECK (((payment_status)::text = ANY ((ARRAY['free'::character varying, 'paying'::character varying])::text[]))),
    CONSTRAINT users_role_check CHECK (((role)::text = ANY ((ARRAY['user'::character varying, 'admin'::character varying])::text[])))
);


ALTER TABLE public.users OWNER TO workwell_user;

--
-- Name: COLUMN users.company_id; Type: COMMENT; Schema: public; Owner: workwell_user
--

COMMENT ON COLUMN public.users.company_id IS 'Explicit company association for user - takes precedence over email domain matching';


--
-- Name: migration_log id; Type: DEFAULT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.migration_log ALTER COLUMN id SET DEFAULT nextval('public.migration_log_id_seq'::regclass);


--
-- Name: activity_log activity_log_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.activity_log
    ADD CONSTRAINT activity_log_pkey PRIMARY KEY (id);


--
-- Name: benefit_categories benefit_categories_name_key; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.benefit_categories
    ADD CONSTRAINT benefit_categories_name_key UNIQUE (name);


--
-- Name: benefit_categories benefit_categories_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.benefit_categories
    ADD CONSTRAINT benefit_categories_pkey PRIMARY KEY (id);


--
-- Name: benefit_removal_disputes benefit_removal_disputes_company_benefit_id_user_id_key; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.benefit_removal_disputes
    ADD CONSTRAINT benefit_removal_disputes_company_benefit_id_user_id_key UNIQUE (company_benefit_id, user_id);


--
-- Name: benefit_removal_disputes benefit_removal_disputes_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.benefit_removal_disputes
    ADD CONSTRAINT benefit_removal_disputes_pkey PRIMARY KEY (id);


--
-- Name: benefit_search_interactions benefit_search_interactions_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.benefit_search_interactions
    ADD CONSTRAINT benefit_search_interactions_pkey PRIMARY KEY (id);


--
-- Name: benefit_verifications benefit_verifications_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.benefit_verifications
    ADD CONSTRAINT benefit_verifications_pkey PRIMARY KEY (id);


--
-- Name: benefits benefits_name_key; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.benefits
    ADD CONSTRAINT benefits_name_key UNIQUE (name);


--
-- Name: benefits benefits_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.benefits
    ADD CONSTRAINT benefits_pkey PRIMARY KEY (id);


--
-- Name: companies companies_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.companies
    ADD CONSTRAINT companies_pkey PRIMARY KEY (id);


--
-- Name: company_analytics_summary company_analytics_summary_company_id_date_key; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_analytics_summary
    ADD CONSTRAINT company_analytics_summary_company_id_date_key UNIQUE (company_id, date);


--
-- Name: company_analytics_summary company_analytics_summary_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_analytics_summary
    ADD CONSTRAINT company_analytics_summary_pkey PRIMARY KEY (id);


--
-- Name: company_benefits company_benefits_company_id_benefit_id_key; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_benefits
    ADD CONSTRAINT company_benefits_company_id_benefit_id_key UNIQUE (company_id, benefit_id);


--
-- Name: company_benefits company_benefits_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_benefits
    ADD CONSTRAINT company_benefits_pkey PRIMARY KEY (id);


--
-- Name: company_page_views company_page_views_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_page_views
    ADD CONSTRAINT company_page_views_pkey PRIMARY KEY (id);


--
-- Name: company_users company_users_company_id_email_key; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_users
    ADD CONSTRAINT company_users_company_id_email_key UNIQUE (company_id, email);


--
-- Name: company_users company_users_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_users
    ADD CONSTRAINT company_users_pkey PRIMARY KEY (id);


--
-- Name: company_verification_tokens company_verification_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_verification_tokens
    ADD CONSTRAINT company_verification_tokens_pkey PRIMARY KEY (id);


--
-- Name: company_verification_tokens company_verification_tokens_token_key; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_verification_tokens
    ADD CONSTRAINT company_verification_tokens_token_key UNIQUE (token);


--
-- Name: daily_analytics_summary daily_analytics_summary_date_key; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.daily_analytics_summary
    ADD CONSTRAINT daily_analytics_summary_date_key UNIQUE (date);


--
-- Name: daily_analytics_summary daily_analytics_summary_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.daily_analytics_summary
    ADD CONSTRAINT daily_analytics_summary_pkey PRIMARY KEY (id);


--
-- Name: magic_link_rate_limits magic_link_rate_limits_email_key; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.magic_link_rate_limits
    ADD CONSTRAINT magic_link_rate_limits_email_key UNIQUE (email);


--
-- Name: magic_link_rate_limits magic_link_rate_limits_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.magic_link_rate_limits
    ADD CONSTRAINT magic_link_rate_limits_pkey PRIMARY KEY (id);


--
-- Name: magic_link_tokens magic_link_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.magic_link_tokens
    ADD CONSTRAINT magic_link_tokens_pkey PRIMARY KEY (id);


--
-- Name: magic_link_tokens magic_link_tokens_token_key; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.magic_link_tokens
    ADD CONSTRAINT magic_link_tokens_token_key UNIQUE (token);


--
-- Name: migration_log migration_log_migration_name_key; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.migration_log
    ADD CONSTRAINT migration_log_migration_name_key UNIQUE (migration_name);


--
-- Name: migration_log migration_log_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.migration_log
    ADD CONSTRAINT migration_log_pkey PRIMARY KEY (id);


--
-- Name: missing_company_reports missing_company_reports_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.missing_company_reports
    ADD CONSTRAINT missing_company_reports_pkey PRIMARY KEY (id);


--
-- Name: saved_companies saved_companies_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.saved_companies
    ADD CONSTRAINT saved_companies_pkey PRIMARY KEY (id);


--
-- Name: saved_companies saved_companies_user_id_company_id_key; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.saved_companies
    ADD CONSTRAINT saved_companies_user_id_company_id_key UNIQUE (user_id, company_id);


--
-- Name: search_queries search_queries_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.search_queries
    ADD CONSTRAINT search_queries_pkey PRIMARY KEY (id);


--
-- Name: user_benefit_rankings user_benefit_rankings_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.user_benefit_rankings
    ADD CONSTRAINT user_benefit_rankings_pkey PRIMARY KEY (id);


--
-- Name: user_benefit_rankings user_benefit_rankings_user_id_benefit_id_key; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.user_benefit_rankings
    ADD CONSTRAINT user_benefit_rankings_user_id_benefit_id_key UNIQUE (user_id, benefit_id);


--
-- Name: user_sessions user_sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_pkey PRIMARY KEY (id);


--
-- Name: user_sessions user_sessions_session_token_key; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_session_token_key UNIQUE (session_token);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: idx_activity_log_benefit_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_activity_log_benefit_id ON public.activity_log USING btree (benefit_id);


--
-- Name: idx_activity_log_company_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_activity_log_company_id ON public.activity_log USING btree (company_id);


--
-- Name: idx_activity_log_created_at; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_activity_log_created_at ON public.activity_log USING btree (created_at DESC);


--
-- Name: idx_activity_log_event_type; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_activity_log_event_type ON public.activity_log USING btree (event_type);


--
-- Name: idx_activity_log_user_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_activity_log_user_id ON public.activity_log USING btree (user_id);


--
-- Name: idx_benefit_categories_is_active; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefit_categories_is_active ON public.benefit_categories USING btree (is_active);


--
-- Name: idx_benefit_categories_name; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefit_categories_name ON public.benefit_categories USING btree (name);


--
-- Name: idx_benefit_categories_sort_order; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefit_categories_sort_order ON public.benefit_categories USING btree (sort_order);


--
-- Name: idx_benefit_removal_disputes_company_benefit_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefit_removal_disputes_company_benefit_id ON public.benefit_removal_disputes USING btree (company_benefit_id);


--
-- Name: idx_benefit_removal_disputes_created_at; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefit_removal_disputes_created_at ON public.benefit_removal_disputes USING btree (created_at);


--
-- Name: idx_benefit_removal_disputes_status; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefit_removal_disputes_status ON public.benefit_removal_disputes USING btree (status);


--
-- Name: idx_benefit_removal_disputes_user_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefit_removal_disputes_user_id ON public.benefit_removal_disputes USING btree (user_id);


--
-- Name: idx_benefit_search_interactions_benefit_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefit_search_interactions_benefit_id ON public.benefit_search_interactions USING btree (benefit_id);


--
-- Name: idx_benefit_search_interactions_company_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefit_search_interactions_company_id ON public.benefit_search_interactions USING btree (company_id);


--
-- Name: idx_benefit_search_interactions_created_at; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefit_search_interactions_created_at ON public.benefit_search_interactions USING btree (created_at);


--
-- Name: idx_benefit_search_interactions_search_query_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefit_search_interactions_search_query_id ON public.benefit_search_interactions USING btree (search_query_id);


--
-- Name: idx_benefit_search_interactions_user_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefit_search_interactions_user_id ON public.benefit_search_interactions USING btree (user_id);


--
-- Name: idx_benefit_verifications_company_benefit_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefit_verifications_company_benefit_id ON public.benefit_verifications USING btree (company_benefit_id);


--
-- Name: idx_benefit_verifications_status; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefit_verifications_status ON public.benefit_verifications USING btree (status);


--
-- Name: idx_benefit_verifications_user_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefit_verifications_user_id ON public.benefit_verifications USING btree (user_id);


--
-- Name: idx_benefits_category; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefits_category ON public.benefits USING btree (category);


--
-- Name: idx_benefits_category_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefits_category_id ON public.benefits USING btree (category_id);


--
-- Name: idx_benefits_name; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_benefits_name ON public.benefits USING btree (name);


--
-- Name: idx_companies_domain; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_companies_domain ON public.companies USING btree (domain);


--
-- Name: idx_companies_industry; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_companies_industry ON public.companies USING btree (industry);


--
-- Name: idx_companies_location; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_companies_location ON public.companies USING btree (location);


--
-- Name: idx_companies_size; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_companies_size ON public.companies USING btree (size);


--
-- Name: idx_company_analytics_summary_company_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_company_analytics_summary_company_id ON public.company_analytics_summary USING btree (company_id);


--
-- Name: idx_company_analytics_summary_date; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_company_analytics_summary_date ON public.company_analytics_summary USING btree (date);


--
-- Name: idx_company_benefits_benefit_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_company_benefits_benefit_id ON public.company_benefits USING btree (benefit_id);


--
-- Name: idx_company_benefits_company_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_company_benefits_company_id ON public.company_benefits USING btree (company_id);


--
-- Name: idx_company_benefits_verified; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_company_benefits_verified ON public.company_benefits USING btree (is_verified);


--
-- Name: idx_company_page_views_company_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_company_page_views_company_id ON public.company_page_views USING btree (company_id);


--
-- Name: idx_company_page_views_created_at; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_company_page_views_created_at ON public.company_page_views USING btree (created_at);


--
-- Name: idx_company_page_views_session_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_company_page_views_session_id ON public.company_page_views USING btree (session_id);


--
-- Name: idx_company_page_views_user_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_company_page_views_user_id ON public.company_page_views USING btree (user_id);


--
-- Name: idx_company_users_company_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_company_users_company_id ON public.company_users USING btree (company_id);


--
-- Name: idx_company_users_email; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_company_users_email ON public.company_users USING btree (email);


--
-- Name: idx_company_verification_tokens_company_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_company_verification_tokens_company_id ON public.company_verification_tokens USING btree (company_id);


--
-- Name: idx_company_verification_tokens_expires_at; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_company_verification_tokens_expires_at ON public.company_verification_tokens USING btree (expires_at);


--
-- Name: idx_company_verification_tokens_token; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_company_verification_tokens_token ON public.company_verification_tokens USING btree (token);


--
-- Name: idx_company_verification_tokens_user_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_company_verification_tokens_user_id ON public.company_verification_tokens USING btree (user_id);


--
-- Name: idx_daily_analytics_summary_date; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_daily_analytics_summary_date ON public.daily_analytics_summary USING btree (date);


--
-- Name: idx_magic_link_rate_limits_email; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_magic_link_rate_limits_email ON public.magic_link_rate_limits USING btree (email);


--
-- Name: idx_magic_link_rate_limits_window_start; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_magic_link_rate_limits_window_start ON public.magic_link_rate_limits USING btree (window_start);


--
-- Name: idx_magic_link_tokens_email; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_magic_link_tokens_email ON public.magic_link_tokens USING btree (email);


--
-- Name: idx_magic_link_tokens_expires_at; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_magic_link_tokens_expires_at ON public.magic_link_tokens USING btree (expires_at);


--
-- Name: idx_magic_link_tokens_token; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_magic_link_tokens_token ON public.magic_link_tokens USING btree (token);


--
-- Name: idx_missing_company_reports_created_at; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_missing_company_reports_created_at ON public.missing_company_reports USING btree (created_at);


--
-- Name: idx_missing_company_reports_email_domain; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_missing_company_reports_email_domain ON public.missing_company_reports USING btree (email_domain);


--
-- Name: idx_missing_company_reports_status; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_missing_company_reports_status ON public.missing_company_reports USING btree (status);


--
-- Name: idx_missing_company_reports_user_email; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_missing_company_reports_user_email ON public.missing_company_reports USING btree (user_email);


--
-- Name: idx_search_queries_created_at; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_search_queries_created_at ON public.search_queries USING btree (created_at);


--
-- Name: idx_search_queries_query_text; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_search_queries_query_text ON public.search_queries USING gin (to_tsvector('english'::regconfig, query_text));


--
-- Name: idx_search_queries_session_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_search_queries_session_id ON public.search_queries USING btree (session_id);


--
-- Name: idx_search_queries_user_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_search_queries_user_id ON public.search_queries USING btree (user_id);


--
-- Name: idx_user_benefit_rankings_benefit_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_user_benefit_rankings_benefit_id ON public.user_benefit_rankings USING btree (benefit_id);


--
-- Name: idx_user_benefit_rankings_ranking; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_user_benefit_rankings_ranking ON public.user_benefit_rankings USING btree (ranking);


--
-- Name: idx_user_benefit_rankings_updated_at; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_user_benefit_rankings_updated_at ON public.user_benefit_rankings USING btree (updated_at);


--
-- Name: idx_user_benefit_rankings_user_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_user_benefit_rankings_user_id ON public.user_benefit_rankings USING btree (user_id);


--
-- Name: idx_user_sessions_token; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_user_sessions_token ON public.user_sessions USING btree (session_token);


--
-- Name: idx_user_sessions_user_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_user_sessions_user_id ON public.user_sessions USING btree (user_id);


--
-- Name: idx_users_company_id; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_users_company_id ON public.users USING btree (company_id);


--
-- Name: idx_users_email; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_users_email ON public.users USING btree (email);


--
-- Name: idx_users_payment_status; Type: INDEX; Schema: public; Owner: workwell_user
--

CREATE INDEX idx_users_payment_status ON public.users USING btree (payment_status);


--
-- Name: benefit_removal_disputes update_benefit_removal_disputes_updated_at; Type: TRIGGER; Schema: public; Owner: workwell_user
--

CREATE TRIGGER update_benefit_removal_disputes_updated_at BEFORE UPDATE ON public.benefit_removal_disputes FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: companies update_companies_updated_at; Type: TRIGGER; Schema: public; Owner: workwell_user
--

CREATE TRIGGER update_companies_updated_at BEFORE UPDATE ON public.companies FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: company_analytics_summary update_company_analytics_summary_updated_at; Type: TRIGGER; Schema: public; Owner: workwell_user
--

CREATE TRIGGER update_company_analytics_summary_updated_at BEFORE UPDATE ON public.company_analytics_summary FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: daily_analytics_summary update_daily_analytics_summary_updated_at; Type: TRIGGER; Schema: public; Owner: workwell_user
--

CREATE TRIGGER update_daily_analytics_summary_updated_at BEFORE UPDATE ON public.daily_analytics_summary FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: missing_company_reports update_missing_company_reports_updated_at; Type: TRIGGER; Schema: public; Owner: workwell_user
--

CREATE TRIGGER update_missing_company_reports_updated_at BEFORE UPDATE ON public.missing_company_reports FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: user_benefit_rankings update_user_benefit_rankings_updated_at; Type: TRIGGER; Schema: public; Owner: workwell_user
--

CREATE TRIGGER update_user_benefit_rankings_updated_at BEFORE UPDATE ON public.user_benefit_rankings FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: users update_users_updated_at; Type: TRIGGER; Schema: public; Owner: workwell_user
--

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: activity_log activity_log_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.activity_log
    ADD CONSTRAINT activity_log_benefit_id_fkey FOREIGN KEY (benefit_id) REFERENCES public.benefits(id) ON DELETE SET NULL;


--
-- Name: activity_log activity_log_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.activity_log
    ADD CONSTRAINT activity_log_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE SET NULL;


--
-- Name: benefit_removal_disputes benefit_removal_disputes_company_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.benefit_removal_disputes
    ADD CONSTRAINT benefit_removal_disputes_company_benefit_id_fkey FOREIGN KEY (company_benefit_id) REFERENCES public.company_benefits(id) ON DELETE CASCADE;


--
-- Name: benefit_search_interactions benefit_search_interactions_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.benefit_search_interactions
    ADD CONSTRAINT benefit_search_interactions_benefit_id_fkey FOREIGN KEY (benefit_id) REFERENCES public.benefits(id) ON DELETE CASCADE;


--
-- Name: benefit_search_interactions benefit_search_interactions_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.benefit_search_interactions
    ADD CONSTRAINT benefit_search_interactions_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: benefit_search_interactions benefit_search_interactions_search_query_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.benefit_search_interactions
    ADD CONSTRAINT benefit_search_interactions_search_query_id_fkey FOREIGN KEY (search_query_id) REFERENCES public.search_queries(id) ON DELETE CASCADE;


--
-- Name: benefit_search_interactions benefit_search_interactions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.benefit_search_interactions
    ADD CONSTRAINT benefit_search_interactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: benefit_verifications benefit_verifications_company_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.benefit_verifications
    ADD CONSTRAINT benefit_verifications_company_benefit_id_fkey FOREIGN KEY (company_benefit_id) REFERENCES public.company_benefits(id) ON DELETE CASCADE;


--
-- Name: benefits benefits_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.benefits
    ADD CONSTRAINT benefits_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.benefit_categories(id);


--
-- Name: company_analytics_summary company_analytics_summary_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_analytics_summary
    ADD CONSTRAINT company_analytics_summary_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_benefits company_benefits_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_benefits
    ADD CONSTRAINT company_benefits_benefit_id_fkey FOREIGN KEY (benefit_id) REFERENCES public.benefits(id) ON DELETE CASCADE;


--
-- Name: company_benefits company_benefits_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_benefits
    ADD CONSTRAINT company_benefits_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_page_views company_page_views_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_page_views
    ADD CONSTRAINT company_page_views_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_page_views company_page_views_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_page_views
    ADD CONSTRAINT company_page_views_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: company_users company_users_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_users
    ADD CONSTRAINT company_users_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_verification_tokens company_verification_tokens_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_verification_tokens
    ADD CONSTRAINT company_verification_tokens_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_verification_tokens company_verification_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.company_verification_tokens
    ADD CONSTRAINT company_verification_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: missing_company_reports missing_company_reports_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.missing_company_reports
    ADD CONSTRAINT missing_company_reports_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE SET NULL;


--
-- Name: saved_companies saved_companies_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.saved_companies
    ADD CONSTRAINT saved_companies_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: search_queries search_queries_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.search_queries
    ADD CONSTRAINT search_queries_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: user_benefit_rankings user_benefit_rankings_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.user_benefit_rankings
    ADD CONSTRAINT user_benefit_rankings_benefit_id_fkey FOREIGN KEY (benefit_id) REFERENCES public.benefits(id) ON DELETE CASCADE;


--
-- Name: user_benefit_rankings user_benefit_rankings_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.user_benefit_rankings
    ADD CONSTRAINT user_benefit_rankings_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_sessions user_sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: users users_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: workwell_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE SET NULL;


--
-- Name: daily_analytics_summary Admin can view all analytics summaries; Type: POLICY; Schema: public; Owner: workwell_user
--

CREATE POLICY "Admin can view all analytics summaries" ON public.daily_analytics_summary FOR SELECT USING (true);


--
-- Name: benefit_search_interactions Admin can view all benefit interactions; Type: POLICY; Schema: public; Owner: workwell_user
--

CREATE POLICY "Admin can view all benefit interactions" ON public.benefit_search_interactions FOR SELECT USING (true);


--
-- Name: company_analytics_summary Admin can view all company analytics; Type: POLICY; Schema: public; Owner: workwell_user
--

CREATE POLICY "Admin can view all company analytics" ON public.company_analytics_summary FOR SELECT USING (true);


--
-- Name: company_page_views Admin can view all company page views; Type: POLICY; Schema: public; Owner: workwell_user
--

CREATE POLICY "Admin can view all company page views" ON public.company_page_views FOR SELECT USING (true);


--
-- Name: search_queries Admin can view all search queries; Type: POLICY; Schema: public; Owner: workwell_user
--

CREATE POLICY "Admin can view all search queries" ON public.search_queries FOR SELECT USING (true);


--
-- Name: benefit_removal_disputes Benefit removal disputes are viewable by admins; Type: POLICY; Schema: public; Owner: workwell_user
--

CREATE POLICY "Benefit removal disputes are viewable by admins" ON public.benefit_removal_disputes FOR SELECT USING (true);


--
-- Name: benefit_removal_disputes; Type: ROW SECURITY; Schema: public; Owner: workwell_user
--

ALTER TABLE public.benefit_removal_disputes ENABLE ROW LEVEL SECURITY;

--
-- Name: benefit_search_interactions; Type: ROW SECURITY; Schema: public; Owner: workwell_user
--

ALTER TABLE public.benefit_search_interactions ENABLE ROW LEVEL SECURITY;

--
-- Name: company_analytics_summary; Type: ROW SECURITY; Schema: public; Owner: workwell_user
--

ALTER TABLE public.company_analytics_summary ENABLE ROW LEVEL SECURITY;

--
-- Name: company_page_views; Type: ROW SECURITY; Schema: public; Owner: workwell_user
--

ALTER TABLE public.company_page_views ENABLE ROW LEVEL SECURITY;

--
-- Name: daily_analytics_summary; Type: ROW SECURITY; Schema: public; Owner: workwell_user
--

ALTER TABLE public.daily_analytics_summary ENABLE ROW LEVEL SECURITY;

--
-- Name: search_queries; Type: ROW SECURITY; Schema: public; Owner: workwell_user
--

ALTER TABLE public.search_queries ENABLE ROW LEVEL SECURITY;

--
-- PostgreSQL database dump complete
--

