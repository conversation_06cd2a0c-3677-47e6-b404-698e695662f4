#!/usr/bin/env node

/**
 * German Companies Data Import Script
 * 
 * This script imports German companies into the WorkWell database.
 * It includes data validation, duplicate checking, and error handling.
 * 
 * Usage:
 *   node scripts/import-german-companies.js [--dry-run] [--source=csv|api|default|dax-mdax]
 *
 * Options:
 *   --dry-run: Preview changes without actually importing
 *   --source: Data source (csv, api, default, or dax-mdax)
 *   --file: Path to CSV file (when using --source=csv)
 *   --index: Filter by index (dax, mdax, or both) when using --source=dax-mdax
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');

// Import DAX/MDAX companies data
const { DAX_COMPANIES, MDAX_COMPANIES, getAllDaxMdaxCompanies } = require('../data/dax-mdax-companies');

// Database configuration
const pool = new Pool({
  user: process.env.DB_USER || 'workwell_user',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'workwell',
  password: process.env.DB_PASSWORD || 'workwell_password',
  port: process.env.DB_PORT || 5432,
});

// Validation constants
const VALID_SIZES = ['startup', 'small', 'medium', 'large', 'enterprise'];
const GERMAN_DOMAIN_REGEX = /^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.de$/;
const GERMAN_LOCATION_KEYWORDS = ['Germany', 'Deutschland', 'Berlin', 'Munich', 'Frankfurt', 'Hamburg', 'Cologne', 'Stuttgart', 'Düsseldorf', 'Dortmund', 'Essen', 'Leipzig', 'Bremen', 'Dresden', 'Hannover'];
const STOCK_TICKER_REGEX = /^[A-Z0-9]{1,6}$/;
const VALID_INDICES = ['DAX', 'MDAX'];

// Curated list of major German companies
const GERMAN_COMPANIES_DATA = [
  {
    name: 'Volkswagen AG',
    location: 'Wolfsburg, Germany',
    size: 'enterprise',
    industry: 'Automotive',
    description: 'German multinational automotive manufacturing company',
    domain: 'volkswagen.de',
    career_url: 'https://www.volkswagen-karriere.de',
    verified: false
  },
  {
    name: 'BMW Group',
    location: 'Munich, Germany', 
    size: 'enterprise',
    industry: 'Automotive',
    description: 'German multinational corporation which produces luxury vehicles and motorcycles',
    domain: 'bmw.de',
    career_url: 'https://www.bmwgroup.jobs',
    verified: false
  },
  {
    name: 'Mercedes-Benz Group AG',
    location: 'Stuttgart, Germany',
    size: 'enterprise', 
    industry: 'Automotive',
    description: 'German multinational automotive corporation',
    domain: 'mercedes-benz.de',
    career_url: 'https://group.mercedes-benz.com/careers',
    verified: false
  },
  {
    name: 'Bosch',
    location: 'Stuttgart, Germany',
    size: 'enterprise',
    industry: 'Technology',
    description: 'German multinational engineering and technology company',
    domain: 'bosch.de',
    career_url: 'https://www.bosch.de/karriere',
    verified: false
  },
  {
    name: 'Adidas AG',
    location: 'Herzogenaurach, Germany',
    size: 'enterprise',
    industry: 'Consumer Goods',
    description: 'German multinational corporation that designs and manufactures shoes, clothing and accessories',
    domain: 'adidas.de',
    career_url: 'https://careers.adidas-group.com',
    verified: false
  },
  {
    name: 'BASF SE',
    location: 'Ludwigshafen, Germany',
    size: 'enterprise',
    industry: 'Chemicals',
    description: 'German multinational chemical company',
    domain: 'basf.de',
    career_url: 'https://www.basf.com/global/en/careers.html',
    verified: false
  },
  {
    name: 'Bayer AG',
    location: 'Leverkusen, Germany',
    size: 'enterprise',
    industry: 'Pharmaceuticals',
    description: 'German multinational pharmaceutical and life sciences company',
    domain: 'bayer.de',
    career_url: 'https://career.bayer.com',
    verified: false
  },
  {
    name: 'Deutsche Telekom AG',
    location: 'Bonn, Germany',
    size: 'enterprise',
    industry: 'Telecommunications',
    description: 'German telecommunications company',
    domain: 'telekom.de',
    career_url: 'https://www.telekom.com/en/careers',
    verified: false
  },
  {
    name: 'Allianz SE',
    location: 'Munich, Germany',
    size: 'enterprise',
    industry: 'Financial Services',
    description: 'German multinational financial services company',
    domain: 'allianz.de',
    career_url: 'https://careers.allianz.com',
    verified: false
  },
  {
    name: 'Zalando SE',
    location: 'Berlin, Germany',
    size: 'large',
    industry: 'E-commerce',
    description: 'German online fashion and lifestyle retailer',
    domain: 'zalando.de',
    career_url: 'https://jobs.zalando.com',
    verified: false
  },
  {
    name: 'Delivery Hero SE',
    location: 'Berlin, Germany',
    size: 'large',
    industry: 'Technology',
    description: 'German multinational online food delivery company',
    domain: 'deliveryhero.de',
    career_url: 'https://careers.deliveryhero.com',
    verified: false
  },
  {
    name: 'N26 GmbH',
    location: 'Berlin, Germany',
    size: 'medium',
    industry: 'Financial Services',
    description: 'German neobank headquartered in Berlin',
    domain: 'n26.de',
    career_url: 'https://n26.com/en/careers',
    verified: false
  },
  {
    name: 'Rocket Internet SE',
    location: 'Berlin, Germany',
    size: 'medium',
    industry: 'Technology',
    description: 'German Internet company that builds and invests in internet and technology companies',
    domain: 'rocket-internet.de',
    career_url: 'https://www.rocket-internet.com/careers',
    verified: false
  },
  {
    name: 'TeamViewer AG',
    location: 'Göppingen, Germany',
    size: 'medium',
    industry: 'Technology',
    description: 'German technology company that provides connectivity software',
    domain: 'teamviewer.de',
    career_url: 'https://www.teamviewer.com/en/careers',
    verified: false
  },
  {
    name: 'HelloFresh SE',
    location: 'Berlin, Germany',
    size: 'large',
    industry: 'Food & Beverage',
    description: 'German meal-kit company based in Berlin',
    domain: 'hellofresh.de',
    career_url: 'https://careers.hellofreshgroup.com',
    verified: false
  }
];

// Logging utility
class Logger {
  constructor(isDryRun = false) {
    this.isDryRun = isDryRun;
    this.stats = {
      processed: 0,
      imported: 0,
      skipped: 0,
      errors: 0
    };
  }

  info(message) {
    console.log(`[INFO] ${message}`);
  }

  warn(message) {
    console.log(`[WARN] ${message}`);
  }

  error(message) {
    console.log(`[ERROR] ${message}`);
  }

  success(message) {
    console.log(`[SUCCESS] ${message}`);
  }

  dryRun(message) {
    if (this.isDryRun) {
      console.log(`[DRY-RUN] ${message}`);
    }
  }

  printStats() {
    console.log('\n=== Import Statistics ===');
    console.log(`Processed: ${this.stats.processed}`);
    console.log(`Imported: ${this.stats.imported}`);
    console.log(`Skipped: ${this.stats.skipped}`);
    console.log(`Errors: ${this.stats.errors}`);
    console.log('========================\n');
  }
}

// Validation functions
function validateCompanyData(company) {
  const errors = [];

  // Required fields
  if (!company.name || company.name.trim().length === 0) {
    errors.push('Company name is required');
  }

  if (!company.location || company.location.trim().length === 0) {
    errors.push('Location is required');
  }

  if (!company.size || !VALID_SIZES.includes(company.size)) {
    errors.push(`Size must be one of: ${VALID_SIZES.join(', ')}`);
  }

  if (!company.industry || company.industry.trim().length === 0) {
    errors.push('Industry is required');
  }

  // German domain validation (relaxed for DAX/MDAX companies)
  if (company.domain && !GERMAN_DOMAIN_REGEX.test(company.domain)) {
    // Allow international domains for DAX/MDAX listed companies
    if (!company.index_membership || !VALID_INDICES.includes(company.index_membership)) {
      errors.push('Domain must be a valid German domain (.de)');
    }
  }

  // German location validation (relaxed for DAX/MDAX companies)
  if (company.location && !GERMAN_LOCATION_KEYWORDS.some(keyword =>
    company.location.toLowerCase().includes(keyword.toLowerCase()))) {
    // Allow non-German locations for DAX/MDAX listed companies (e.g., ASML from Netherlands)
    if (!company.index_membership || !VALID_INDICES.includes(company.index_membership)) {
      errors.push('Location must be in Germany');
    }
  }

  // URL validation
  if (company.career_url && !isValidUrl(company.career_url)) {
    errors.push('Career URL must be a valid URL');
  }

  // Stock ticker validation (optional field for DAX/MDAX companies)
  if (company.ticker && !STOCK_TICKER_REGEX.test(company.ticker)) {
    errors.push('Stock ticker must be 1-6 uppercase letters/numbers');
  }

  // Index membership validation (optional field)
  if (company.index_membership && !VALID_INDICES.includes(company.index_membership)) {
    errors.push(`Index membership must be one of: ${VALID_INDICES.join(', ')}`);
  }

  return errors;
}

function isValidUrl(string) {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

// Database operations
async function checkDuplicateCompany(company) {
  const client = await pool.connect();
  try {
    // Check by name and domain
    const query = `
      SELECT id, name, domain 
      FROM companies 
      WHERE LOWER(name) = LOWER($1) 
         OR (domain IS NOT NULL AND LOWER(domain) = LOWER($2))
    `;
    const result = await client.query(query, [company.name, company.domain || '']);
    return result.rows.length > 0 ? result.rows[0] : null;
  } finally {
    client.release();
  }
}

async function insertCompany(company, logger) {
  const client = await pool.connect();
  try {
    const query = `
      INSERT INTO companies (name, location, size, industry, description, domain, career_url, verified)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING id, name
    `;

    // Enhance description with stock ticker and index information for DAX/MDAX companies
    let enhancedDescription = company.description?.trim() || '';
    if (company.ticker || company.index_membership) {
      const stockInfo = [];
      if (company.index_membership) {
        stockInfo.push(`${company.index_membership}-listed`);
      }
      if (company.ticker) {
        stockInfo.push(`Stock: ${company.ticker}`);
      }
      if (stockInfo.length > 0) {
        enhancedDescription = stockInfo.join(' | ') + (enhancedDescription ? ` | ${enhancedDescription}` : '');
      }
    }

    const values = [
      company.name.trim(),
      company.location.trim(),
      company.size,
      company.industry.trim(),
      enhancedDescription || null,
      company.domain?.toLowerCase() || null,
      company.career_url || null,
      company.verified || false
    ];

    if (logger.isDryRun) {
      logger.dryRun(`Would insert company: ${company.name} (${company.domain})`);
      return { id: 'dry-run-id', name: company.name };
    }

    const result = await client.query(query, values);
    return result.rows[0];
  } finally {
    client.release();
  }
}

// CSV parsing function
async function parseCSVFile(filePath) {
  return new Promise((resolve, reject) => {
    const companies = [];
    const errors = [];

    if (!fs.existsSync(filePath)) {
      reject(new Error(`CSV file not found: ${filePath}`));
      return;
    }

    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (row) => {
        try {
          // Map CSV columns to company object
          const company = {
            name: row.name || row.company_name || row.Name,
            location: row.location || row.Location,
            size: row.size || row.Size || 'medium',
            industry: row.industry || row.Industry,
            description: row.description || row.Description,
            domain: row.domain || row.Domain,
            career_url: row.career_url || row.career_page || row.CareerURL,
            verified: row.verified === 'true' || row.verified === true || false,
            ticker: row.ticker || row.stock_ticker || row.Ticker,
            index_membership: row.index_membership || row.index || row.Index
          };

          companies.push(company);
        } catch (error) {
          errors.push(`Error parsing row: ${error.message}`);
        }
      })
      .on('end', () => {
        if (errors.length > 0) {
          reject(new Error(`CSV parsing errors: ${errors.join(', ')}`));
        } else {
          resolve(companies);
        }
      })
      .on('error', reject);
  });
}

// Main import function
async function importCompanies(companies, logger) {
  logger.info(`Starting import of ${companies.length} companies...`);

  for (const company of companies) {
    logger.stats.processed++;

    try {
      // Validate company data
      const validationErrors = validateCompanyData(company);
      if (validationErrors.length > 0) {
        logger.error(`Validation failed for ${company.name}: ${validationErrors.join(', ')}`);
        logger.stats.errors++;
        continue;
      }

      // Check for duplicates
      const duplicate = await checkDuplicateCompany(company);
      if (duplicate) {
        logger.warn(`Skipping duplicate company: ${company.name} (existing: ${duplicate.name})`);
        logger.stats.skipped++;
        continue;
      }

      // Insert company
      const result = await insertCompany(company, logger);
      logger.success(`Imported: ${result.name} (ID: ${result.id})`);
      logger.stats.imported++;

    } catch (error) {
      logger.error(`Failed to import ${company.name}: ${error.message}`);
      logger.stats.errors++;
    }
  }

  logger.printStats();
}

// Data source handlers
async function getCompaniesFromSource(source, options = {}) {
  switch (source) {
    case 'csv':
      if (!options.file) {
        throw new Error('CSV file path is required when using --source=csv');
      }
      return await parseCSVFile(options.file);

    case 'api':
      // Placeholder for future API integration
      throw new Error('API source not yet implemented. Use --source=default, --source=csv, or --source=dax-mdax');

    case 'dax-mdax':
      // Filter by index if specified
      if (options.index) {
        const indexFilter = options.index.toUpperCase();
        if (indexFilter === 'DAX') {
          return DAX_COMPANIES;
        } else if (indexFilter === 'MDAX') {
          return MDAX_COMPANIES;
        } else {
          throw new Error('Index filter must be "dax" or "mdax"');
        }
      }
      return getAllDaxMdaxCompanies();

    case 'default':
    default:
      return GERMAN_COMPANIES_DATA;
  }
}

// CLI argument parsing
function parseArguments() {
  const args = process.argv.slice(2);
  const options = {
    dryRun: false,
    source: 'default',
    file: null,
    index: null
  };

  for (const arg of args) {
    if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg.startsWith('--source=')) {
      options.source = arg.split('=')[1];
    } else if (arg.startsWith('--file=')) {
      options.file = arg.split('=')[1];
    } else if (arg.startsWith('--index=')) {
      options.index = arg.split('=')[1];
    } else if (arg === '--help' || arg === '-h') {
      printHelp();
      process.exit(0);
    }
  }

  return options;
}

function printHelp() {
  console.log(`
German Companies Data Import Script

Usage:
  node scripts/import-german-companies.js [options]

Options:
  --dry-run              Preview changes without actually importing
  --source=SOURCE        Data source: csv, api, default, or dax-mdax (default: default)
  --file=PATH           Path to CSV file (required when using --source=csv)
  --index=INDEX         Filter by index: dax or mdax (when using --source=dax-mdax)
  --help, -h            Show this help message

Examples:
  node scripts/import-german-companies.js
  node scripts/import-german-companies.js --dry-run
  node scripts/import-german-companies.js --source=csv --file=./data/german-companies.csv
  node scripts/import-german-companies.js --source=dax-mdax
  node scripts/import-german-companies.js --source=dax-mdax --index=dax --dry-run
  node scripts/import-german-companies.js --source=dax-mdax --index=mdax

CSV Format:
  The CSV file should contain the following columns:
  - name (required): Company name
  - location (required): Company location in Germany
  - size (required): startup, small, medium, large, or enterprise
  - industry (required): Industry category
  - description (optional): Company description
  - domain (optional): Company domain (.de domains only)
  - career_url (optional): URL to company careers page
  - verified (optional): true/false for verification status
  - ticker (optional): Stock ticker symbol (for DAX/MDAX companies)
  - index_membership (optional): DAX or MDAX

DAX/MDAX Source:
  Imports current DAX (40 companies) and MDAX (50+ companies) listings
  Includes stock ticker symbols and index membership information
  Use --index=dax or --index=mdax to filter by specific index
`);
}

// Main execution
async function main() {
  try {
    const options = parseArguments();
    const logger = new Logger(options.dryRun);

    logger.info('German Companies Import Script');
    logger.info(`Source: ${options.source}`);
    logger.info(`Dry run: ${options.dryRun}`);

    if (options.dryRun) {
      logger.info('DRY RUN MODE - No changes will be made to the database');
    }

    // Get companies data
    const companies = await getCompaniesFromSource(options.source, options);

    // Import companies
    await importCompanies(companies, logger);

    logger.info('Import completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error(`[FATAL] ${error.message}`);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  importCompanies,
  validateCompanyData,
  parseCSVFile,
  GERMAN_COMPANIES_DATA
};
