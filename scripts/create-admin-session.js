const { Pool } = require('pg')

async function createAdminSession() {
  const pool = new Pool({
    host: 'localhost',
    port: 5432,
    database: 'workwell',
    user: 'workwell_user',
    password: 'workwell_password',
  })

  try {
    const client = await pool.connect()
    console.log('Creating admin session...')
    
    // Get the admin user
    const adminUser = await client.query(`
      SELECT id, email, payment_status, role FROM users WHERE email = '<EMAIL>'
    `)
    
    if (adminUser.rows.length === 0) {
      console.log('❌ Admin user not found')
      return
    }
    
    const user = adminUser.rows[0]
    console.log(`Found user: ${user.email} (${user.payment_status}, ${user.role})`)
    
    // Create a session token
    const sessionToken = `admin_session_${Date.now()}`
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
    
    // Delete any existing sessions for this user
    await client.query('DELETE FROM user_sessions WHERE user_id = $1', [user.id])
    
    // Create new session
    await client.query(`
      INSERT INTO user_sessions (user_id, session_token, expires_at)
      VALUES ($1, $2, $3)
    `, [user.id, sessionToken, expiresAt])
    
    console.log(`✅ Session created: ${sessionToken}`)
    console.log(`Expires at: ${expiresAt.toISOString()}`)
    
    client.release()
    await pool.end()
    
    console.log('\n🎉 Admin session created successfully!')
    console.log('\nTo test the API:')
    console.log(`curl -X GET "http://localhost:3001/api/analytics/benefit-rankings?period=30d&limit=20" \\`)
    console.log(`  -H "Content-Type: application/json" \\`)
    console.log(`  -H "Cookie: session_token=${sessionToken}"`)
    
    console.log('\nTo test in browser:')
    console.log('1. Open browser developer tools')
    console.log('2. Go to Application/Storage > Cookies > http://localhost:3001')
    console.log(`3. Add cookie: session_token = ${sessionToken}`)
    console.log('4. Refresh the page and go to Analytics > Benefit Rankings')
    
  } catch (error) {
    console.error('Error:', error)
    await pool.end()
  }
}

createAdminSession()
