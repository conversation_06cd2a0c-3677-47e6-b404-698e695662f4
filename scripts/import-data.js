#!/usr/bin/env node

/**
 * Generic Data Import Script for WorkWell
 * 
 * This script can import companies or benefits from CSV or JSON files with configurable
 * field mappings and validation rules.
 * 
 * Usage:
 *   node scripts/import-data.js --type=companies --file=data.csv [options]
 *   node scripts/import-data.js --type=benefits --file=data.json [options]
 * 
 * Options:
 *   --type: Data type to import (companies|benefits) [required]
 *   --file: Path to data file (CSV or JSON) [required]
 *   --format: File format (csv|json) [auto-detected from extension]
 *   --mapping: Path to field mapping configuration file [optional]
 *   --dry-run: Preview changes without actually importing
 *   --category: Filter benefits by category (only for benefits import)
 *   --help: Show this help message
 */

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  user: process.env.DB_USER || 'workwell_user',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'workwell',
  password: process.env.DB_PASSWORD || 'workwell_password',
  port: process.env.DB_PORT || 5432,
});

// Logger class for consistent output
class Logger {
  constructor(isDryRun = false) {
    this.isDryRun = isDryRun;
    this.stats = {
      processed: 0,
      imported: 0,
      skipped: 0,
      errors: 0
    };
  }

  info(message) {
    console.log(`[INFO] ${message}`);
  }

  success(message) {
    console.log(`[SUCCESS] ${message}`);
  }

  warn(message) {
    console.log(`[WARN] ${message}`);
  }

  error(message) {
    console.log(`[ERROR] ${message}`);
  }

  dryRun(message) {
    console.log(`[DRY-RUN] ${message}`);
  }

  printStats() {
    console.log('\n=== Import Statistics ===');
    console.log(`Processed: ${this.stats.processed}`);
    console.log(`Imported: ${this.stats.imported}`);
    console.log(`Skipped: ${this.stats.skipped}`);
    console.log(`Errors: ${this.stats.errors}`);
  }
}

// Default field mappings
const DEFAULT_MAPPINGS = {
  companies: {
    name: ['name', 'company_name', 'Company Name', 'Name'],
    location: ['location', 'Location', 'city', 'City'],
    size: ['size', 'Size', 'company_size', 'Company Size'],
    industry: ['industry', 'Industry', 'sector', 'Sector'],
    description: ['description', 'Description', 'about', 'About'],
    domain: ['domain', 'Domain', 'website', 'Website'],
    career_url: ['career_url', 'career_page', 'CareerURL', 'Career URL', 'careers'],
    ticker: ['ticker', 'stock_ticker', 'Ticker', 'Stock Ticker'],
    index_membership: ['index_membership', 'index', 'Index', 'stock_index']
  },
  benefits: {
    name: ['name', 'benefit_name', 'Benefit Name', 'Name'],
    category: ['category', 'Category', 'type', 'Type'],
    icon: ['icon', 'Icon', 'emoji', 'Emoji'],
    description: ['description', 'Description', 'details', 'Details']
  }
};

// Valid values for enum fields
const VALID_VALUES = {
  companies: {
    size: ['startup', 'small', 'medium', 'large', 'enterprise']
  },
  benefits: {
    category: ['health', 'time_off', 'financial', 'development', 'wellness', 'work_life', 'other']
  }
};

// Parse command line arguments
function parseArguments() {
  const args = process.argv.slice(2);
  const options = {
    type: null,
    file: null,
    format: null,
    mapping: null,
    dryRun: false,
    category: null,
    help: false
  };

  for (const arg of args) {
    if (arg === '--help' || arg === '-h') {
      options.help = true;
    } else if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg.startsWith('--type=')) {
      options.type = arg.split('=')[1];
    } else if (arg.startsWith('--file=')) {
      options.file = arg.split('=')[1];
    } else if (arg.startsWith('--format=')) {
      options.format = arg.split('=')[1];
    } else if (arg.startsWith('--mapping=')) {
      options.mapping = arg.split('=')[1];
    } else if (arg.startsWith('--category=')) {
      options.category = arg.split('=')[1];
    }
  }

  return options;
}

// Show help message
function showHelp() {
  console.log(`
Generic Data Import Script for WorkWell

Usage:
  node scripts/import-data.js --type=companies --file=data.csv [options]
  node scripts/import-data.js --type=benefits --file=data.json [options]

Options:
  --type=TYPE        Data type to import (companies|benefits) [required]
  --file=FILE        Path to data file (CSV or JSON) [required]
  --format=FORMAT    File format (csv|json) [auto-detected from extension]
  --mapping=FILE     Path to field mapping configuration file [optional]
  --dry-run          Preview changes without actually importing
  --category=CAT     Filter benefits by category (only for benefits import)
  --help, -h         Show this help message

Examples:
  node scripts/import-data.js --type=companies --file=data/demo-companies.csv
  node scripts/import-data.js --type=benefits --file=data/demo-benefits.json --dry-run
  node scripts/import-data.js --type=companies --file=custom.csv --mapping=mappings/custom.json

Supported Data Types:
  companies: Company information (name, location, size, industry, etc.)
  benefits:  Employee benefits (name, category, icon, description)

File Formats:
  CSV: Comma-separated values with header row
  JSON: Array of objects or single object

Field Mappings:
  The script automatically maps common field names. Use --mapping for custom mappings.
  See documentation for detailed field mapping configuration.
`);
}

// Load field mappings from file or use defaults
function loadFieldMappings(type, mappingFile = null) {
  let mappings = DEFAULT_MAPPINGS[type];

  if (mappingFile && fs.existsSync(mappingFile)) {
    try {
      const customMappings = JSON.parse(fs.readFileSync(mappingFile, 'utf8'));
      if (customMappings[type]) {
        mappings = { ...mappings, ...customMappings[type] };
      }
    } catch (error) {
      console.warn(`[WARN] Failed to load custom mappings: ${error.message}`);
    }
  }

  return mappings;
}

// Map field value from source data using field mappings
function mapFieldValue(sourceData, fieldMappings) {
  for (const possibleField of fieldMappings) {
    if (sourceData.hasOwnProperty(possibleField) && sourceData[possibleField] !== undefined && sourceData[possibleField] !== '') {
      return sourceData[possibleField];
    }
  }
  return null;
}

// Parse CSV file
async function parseCSVFile(filePath) {
  return new Promise((resolve, reject) => {
    const data = [];
    const errors = [];

    if (!fs.existsSync(filePath)) {
      reject(new Error(`CSV file not found: ${filePath}`));
      return;
    }

    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (row) => {
        try {
          data.push(row);
        } catch (error) {
          errors.push(`Error parsing row: ${error.message}`);
        }
      })
      .on('end', () => {
        if (errors.length > 0) {
          console.warn(`[WARN] ${errors.length} parsing errors occurred`);
          errors.forEach(error => console.warn(`[WARN] ${error}`));
        }
        resolve(data);
      })
      .on('error', (error) => {
        reject(error);
      });
  });
}

// Parse JSON file
async function parseJSONFile(filePath) {
  if (!fs.existsSync(filePath)) {
    throw new Error(`JSON file not found: ${filePath}`);
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(content);
    
    // Ensure data is an array
    return Array.isArray(data) ? data : [data];
  } catch (error) {
    throw new Error(`Failed to parse JSON file: ${error.message}`);
  }
}

// Auto-detect file format from extension
function detectFileFormat(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  switch (ext) {
    case '.csv':
      return 'csv';
    case '.json':
      return 'json';
    default:
      throw new Error(`Unsupported file format: ${ext}. Supported formats: .csv, .json`);
  }
}

// Transform source data to target format using field mappings
function transformData(sourceData, type, fieldMappings) {
  const transformed = {};

  for (const [targetField, sourceMappings] of Object.entries(fieldMappings)) {
    const value = mapFieldValue(sourceData, sourceMappings);

    if (value !== null) {
      // Apply type-specific transformations
      switch (targetField) {
        case 'domain':
          transformed[targetField] = value.toLowerCase().replace(/^https?:\/\//, '').replace(/\/$/, '');
          break;
        case 'size':
          if (type === 'companies') {
            const normalizedSize = value.toLowerCase();
            transformed[targetField] = VALID_VALUES.companies.size.includes(normalizedSize) ? normalizedSize : 'medium';
          } else {
            transformed[targetField] = value;
          }
          break;
        case 'category':
          if (type === 'benefits') {
            const normalizedCategory = value.toLowerCase();
            transformed[targetField] = VALID_VALUES.benefits.category.includes(normalizedCategory) ? normalizedCategory : 'other';
          } else {
            transformed[targetField] = value;
          }
          break;
        default:
          transformed[targetField] = typeof value === 'string' ? value.trim() : value;
      }
    }
  }

  return transformed;
}

// Validate company data
function validateCompanyData(company) {
  const errors = [];

  if (!company.name || company.name.trim() === '') {
    errors.push('Company name is required');
  }

  if (!company.location || company.location.trim() === '') {
    errors.push('Company location is required');
  }

  if (!company.industry || company.industry.trim() === '') {
    errors.push('Company industry is required');
  }

  if (company.size && !VALID_VALUES.companies.size.includes(company.size)) {
    errors.push(`Invalid company size: ${company.size}. Valid values: ${VALID_VALUES.companies.size.join(', ')}`);
  }

  if (company.domain && !/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/.test(company.domain)) {
    errors.push('Invalid domain format');
  }

  return errors;
}

// Validate benefit data
function validateBenefitData(benefit) {
  const errors = [];

  if (!benefit.name || benefit.name.trim() === '') {
    errors.push('Benefit name is required');
  }

  if (!benefit.category || benefit.category.trim() === '') {
    errors.push('Benefit category is required');
  }

  if (benefit.category && !VALID_VALUES.benefits.category.includes(benefit.category)) {
    errors.push(`Invalid benefit category: ${benefit.category}. Valid values: ${VALID_VALUES.benefits.category.join(', ')}`);
  }

  return errors;
}

// Check for duplicate company
async function checkDuplicateCompany(company, isDryRun = false) {
  if (isDryRun) {
    return null; // Skip duplicate check in dry-run mode
  }

  const client = await pool.connect();
  try {
    const query = `
      SELECT id, name FROM companies
      WHERE LOWER(name) = LOWER($1) OR (domain IS NOT NULL AND LOWER(domain) = LOWER($2))
    `;
    const result = await client.query(query, [company.name, company.domain || '']);
    return result.rows.length > 0 ? result.rows[0] : null;
  } finally {
    client.release();
  }
}

// Check for duplicate benefit
async function checkDuplicateBenefit(benefit, isDryRun = false) {
  if (isDryRun) {
    return null; // Skip duplicate check in dry-run mode
  }

  const client = await pool.connect();
  try {
    const query = `SELECT id, name FROM benefits WHERE LOWER(name) = LOWER($1)`;
    const result = await client.query(query, [benefit.name]);
    return result.rows.length > 0 ? result.rows[0] : null;
  } finally {
    client.release();
  }
}

// Insert company into database
async function insertCompany(company, logger) {
  if (logger.isDryRun) {
    logger.dryRun(`Would insert company: ${company.name} (${company.domain || 'no domain'})`);
    return { id: 'dry-run-id', name: company.name };
  }

  const client = await pool.connect();
  try {
    const query = `
      INSERT INTO companies (name, location, size, industry, description, domain, career_url)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING id, name
    `;

    // Enhance description with additional info if available
    let enhancedDescription = company.description?.trim() || '';
    if (company.ticker || company.index_membership) {
      const stockInfo = [];
      if (company.index_membership) {
        stockInfo.push(`${company.index_membership}-listed`);
      }
      if (company.ticker) {
        stockInfo.push(`Stock: ${company.ticker}`);
      }
      if (stockInfo.length > 0) {
        enhancedDescription = stockInfo.join(' | ') + (enhancedDescription ? ` | ${enhancedDescription}` : '');
      }
    }

    const values = [
      company.name.trim(),
      company.location.trim(),
      company.size || 'medium',
      company.industry.trim(),
      enhancedDescription || null,
      company.domain?.toLowerCase() || null,
      company.career_url || null,
    ];



    const result = await client.query(query, values);
    return result.rows[0];
  } finally {
    client.release();
  }
}

// Insert benefit into database
async function insertBenefit(benefit, logger) {
  if (logger.isDryRun) {
    logger.dryRun(`Would insert benefit: ${benefit.name} (${benefit.category})`);
    return { id: 'dry-run-id', name: benefit.name };
  }

  const client = await pool.connect();
  try {
    const query = `
      INSERT INTO benefits (name, category, icon, description)
      VALUES ($1, $2, $3, $4)
      RETURNING id, name
    `;

    const values = [
      benefit.name.trim(),
      benefit.category,
      benefit.icon || null,
      benefit.description || null
    ];



    const result = await client.query(query, values);
    return result.rows[0];
  } finally {
    client.release();
  }
}

// Import companies
async function importCompanies(companies, logger) {
  logger.info(`Starting import of ${companies.length} companies...`);

  for (const company of companies) {
    logger.stats.processed++;

    try {
      // Validate company data
      const validationErrors = validateCompanyData(company);
      if (validationErrors.length > 0) {
        logger.error(`Validation failed for ${company.name || 'unknown'}: ${validationErrors.join(', ')}`);
        logger.stats.errors++;
        continue;
      }

      // Check for duplicates
      const duplicate = await checkDuplicateCompany(company, logger.isDryRun);
      if (duplicate) {
        logger.warn(`Skipping duplicate company: ${company.name} (existing: ${duplicate.name})`);
        logger.stats.skipped++;
        continue;
      }

      // Insert company
      const result = await insertCompany(company, logger);
      logger.success(`Imported: ${result.name} (ID: ${result.id})`);
      logger.stats.imported++;

    } catch (error) {
      logger.error(`Failed to import company ${company.name || 'unknown'}: ${error.message}`);
      logger.stats.errors++;
    }
  }
}

// Import benefits
async function importBenefits(benefits, logger) {
  logger.info(`Starting import of ${benefits.length} benefits...`);

  for (const benefit of benefits) {
    logger.stats.processed++;

    try {
      // Validate benefit data
      const validationErrors = validateBenefitData(benefit);
      if (validationErrors.length > 0) {
        logger.error(`Validation failed for ${benefit.name || 'unknown'}: ${validationErrors.join(', ')}`);
        logger.stats.errors++;
        continue;
      }

      // Check for duplicates
      const duplicate = await checkDuplicateBenefit(benefit, logger.isDryRun);
      if (duplicate) {
        logger.warn(`Skipping duplicate benefit: ${benefit.name} (existing: ${duplicate.name})`);
        logger.stats.skipped++;
        continue;
      }

      // Insert benefit
      const result = await insertBenefit(benefit, logger);
      logger.success(`Imported: ${result.name} (ID: ${result.id})`);
      logger.stats.imported++;

    } catch (error) {
      logger.error(`Failed to import benefit ${benefit.name || 'unknown'}: ${error.message}`);
      logger.stats.errors++;
    }
  }
}

// Load and parse data file
async function loadDataFile(filePath, format) {
  switch (format) {
    case 'csv':
      return await parseCSVFile(filePath);
    case 'json':
      return await parseJSONFile(filePath);
    default:
      throw new Error(`Unsupported format: ${format}`);
  }
}

// Main execution function
async function main() {
  const options = parseArguments();

  try {

    if (options.help) {
      showHelp();
      process.exit(0);
    }

    // Validate required options
    if (!options.type) {
      throw new Error('--type is required. Use --help for usage information.');
    }

    if (!options.file) {
      throw new Error('--file is required. Use --help for usage information.');
    }

    if (!['companies', 'benefits'].includes(options.type)) {
      throw new Error('--type must be either "companies" or "benefits"');
    }

    // Auto-detect format if not specified
    if (!options.format) {
      options.format = detectFileFormat(options.file);
    }

    const logger = new Logger(options.dryRun);

    logger.info('Generic Data Import Script for WorkWell');
    logger.info(`Type: ${options.type}`);
    logger.info(`File: ${options.file}`);
    logger.info(`Format: ${options.format}`);
    logger.info(`Dry run: ${options.dryRun}`);

    if (options.dryRun) {
      logger.info('DRY RUN MODE - No changes will be made to the database');
    }

    // Load field mappings
    const fieldMappings = loadFieldMappings(options.type, options.mapping);

    // Load and parse data file
    logger.info('Loading data file...');
    const sourceData = await loadDataFile(options.file, options.format);
    logger.info(`Loaded ${sourceData.length} records from file`);

    // Transform data using field mappings
    logger.info('Transforming data...');
    const transformedData = sourceData.map(item => transformData(item, options.type, fieldMappings));

    // Filter by category if specified (benefits only)
    let dataToImport = transformedData;
    if (options.type === 'benefits' && options.category) {
      if (!VALID_VALUES.benefits.category.includes(options.category)) {
        throw new Error(`Invalid category: ${options.category}. Valid categories: ${VALID_VALUES.benefits.category.join(', ')}`);
      }
      dataToImport = transformedData.filter(item => item.category === options.category);
      logger.info(`Filtered to ${dataToImport.length} benefits in category: ${options.category}`);
    }

    // Import data
    if (options.type === 'companies') {
      await importCompanies(dataToImport, logger);
    } else {
      await importBenefits(dataToImport, logger);
    }

    logger.printStats();
    logger.info('Import completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error(`[FATAL] ${error.message}`);
    process.exit(1);
  } finally {
    // Only close pool if not in dry-run mode (to avoid connection attempts)
    if (!options.dryRun) {
      await pool.end();
    }
  }
}

// Run the script
if (require.main === module) {
  main();
}
