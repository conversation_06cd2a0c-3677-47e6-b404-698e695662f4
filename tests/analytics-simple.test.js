/**
 * Simple Analytics System Tests
 * Tests analytics tracking without authentication requirements
 */

const { execSync } = require('child_process');
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

// Test configuration
const BASE_URL = 'http://localhost:3001';

// Helper functions
async function makeRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  });
  
  const data = await response.json();
  return { response, data };
}

async function queryDatabase(sql) {
  try {
    const result = execSync(`docker exec -i workwell-postgres psql -U workwell_user -d workwell -t -c "${sql}"`, {
      encoding: 'utf8'
    });
    return result.trim();
  } catch (error) {
    console.error('Database query failed:', error.message);
    return null;
  }
}

async function resetAnalyticsData() {
  console.log('Resetting analytics data...');
  const sql = `
    DELETE FROM benefit_search_interactions;
    DELETE FROM search_queries;
    DELETE FROM company_page_views;
    DELETE FROM daily_analytics_summary;
    DELETE FROM company_analytics_summary;
  `;
  
  try {
    execSync(`docker exec -i workwell-postgres psql -U workwell_user -d workwell -c "${sql.replace(/\n/g, ' ')}"`, {
      stdio: 'pipe'
    });
    console.log('✓ Analytics data reset');
  } catch (error) {
    console.warn('Failed to reset analytics data:', error.message);
  }
}

async function insertTestData() {
  console.log('Using existing test data...');

  // Just verify we have some companies and benefits to work with
  const companyCount = await queryDatabase('SELECT COUNT(*) FROM companies;');
  const benefitCount = await queryDatabase('SELECT COUNT(*) FROM benefits;');

  console.log(`✓ Found ${companyCount} companies and ${benefitCount} benefits in database`);
}

async function getTestCompanyId() {
  const result = await queryDatabase("SELECT id FROM companies LIMIT 1;");
  return result ? result.trim() : null;
}

async function getTestBenefitId() {
  const result = await queryDatabase("SELECT id FROM benefits LIMIT 1;");
  return result ? result.trim() : null;
}

async function testAnalyticsTracking() {
  console.log('\n=== Testing Analytics Tracking ===');
  
  const companyId = await getTestCompanyId();
  const benefitId = await getTestBenefitId();
  
  if (!companyId || !benefitId) {
    throw new Error('Test data not found - company or benefit missing');
  }
  
  console.log(`Using test company ID: ${companyId}`);
  console.log(`Using test benefit ID: ${benefitId}`);
  
  // Test company view tracking
  console.log('Testing company view tracking...');
  const { response: trackResponse, data: trackData } = await makeRequest('/api/analytics/track', {
    method: 'POST',
    body: JSON.stringify({
      type: 'company_view',
      data: {
        companyId: companyId,
        referrer: 'https://example.com'
      }
    })
  });
  
  if (!trackResponse.ok) {
    throw new Error(`Company view tracking failed: ${trackData.error}`);
  }
  console.log('✓ Company view tracking successful');
  
  // Verify in database
  const viewCount = await queryDatabase('SELECT COUNT(*) FROM company_page_views;');
  if (parseInt(viewCount) < 1) {
    throw new Error('Company view not recorded in database');
  }
  console.log(`✓ Company view recorded in database (${viewCount} total views)`);
  
  // Test search tracking
  console.log('Testing search tracking...');
  const { response: searchResponse, data: searchData } = await makeRequest('/api/analytics/track', {
    method: 'POST',
    body: JSON.stringify({
      type: 'search',
      data: {
        queryText: 'health benefits',
        resultsCount: 5,
        filtersApplied: {
          location: 'Test City',
          size: 'medium'
        }
      }
    })
  });
  
  if (!searchResponse.ok) {
    throw new Error(`Search tracking failed: ${searchData.error}`);
  }
  console.log('✓ Search tracking successful');
  
  // Verify in database
  const searchCount = await queryDatabase('SELECT COUNT(*) FROM search_queries;');
  if (parseInt(searchCount) < 1) {
    throw new Error('Search not recorded in database');
  }
  console.log(`✓ Search recorded in database (${searchCount} total searches)`);
  
  // Test benefit interaction tracking
  console.log('Testing benefit interaction tracking...');
  const { response: interactionResponse, data: interactionData } = await makeRequest('/api/analytics/track', {
    method: 'POST',
    body: JSON.stringify({
      type: 'benefit_interaction',
      data: {
        benefitId: benefitId,
        companyId: companyId,
        interactionType: 'verify',
        searchQueryId: searchData.searchId
      }
    })
  });
  
  if (!interactionResponse.ok) {
    throw new Error(`Benefit interaction tracking failed: ${interactionData.error}`);
  }
  console.log('✓ Benefit interaction tracking successful');
  
  // Verify in database
  const interactionCount = await queryDatabase('SELECT COUNT(*) FROM benefit_search_interactions;');
  if (parseInt(interactionCount) < 1) {
    throw new Error('Benefit interaction not recorded in database');
  }
  console.log(`✓ Benefit interaction recorded in database (${interactionCount} total interactions)`);
}

async function testAnalyticsSummaryFunctions() {
  console.log('\n=== Testing Analytics Summary Functions ===');
  
  // Test daily analytics summary function
  console.log('Testing daily analytics summary function...');
  try {
    await queryDatabase('SELECT update_daily_analytics_summary(CURRENT_DATE);');
    console.log('✓ Daily analytics summary function executed');
    
    const summaryCount = await queryDatabase('SELECT COUNT(*) FROM daily_analytics_summary;');
    console.log(`✓ Daily summary records: ${summaryCount}`);
  } catch (error) {
    throw new Error(`Daily analytics summary function failed: ${error.message}`);
  }
  
  // Test company analytics summary function
  console.log('Testing company analytics summary function...');
  const companyId = await getTestCompanyId();
  if (companyId) {
    try {
      await queryDatabase(`SELECT update_company_analytics_summary('${companyId}', CURRENT_DATE);`);
      console.log('✓ Company analytics summary function executed');
      
      const companySummaryCount = await queryDatabase('SELECT COUNT(*) FROM company_analytics_summary;');
      console.log(`✓ Company summary records: ${companySummaryCount}`);
    } catch (error) {
      throw new Error(`Company analytics summary function failed: ${error.message}`);
    }
  }
}

async function testDataIntegrity() {
  console.log('\n=== Testing Data Integrity ===');
  
  const companyId = await getTestCompanyId();
  const benefitId = await getTestBenefitId();
  
  // Track multiple events
  console.log('Tracking multiple test events...');
  
  // Track 3 company views
  for (let i = 0; i < 3; i++) {
    await makeRequest('/api/analytics/track', {
      method: 'POST',
      body: JSON.stringify({
        type: 'company_view',
        data: {
          companyId: companyId,
          referrer: `https://test${i}.com`
        }
      })
    });
  }
  
  // Track 2 searches
  for (let i = 0; i < 2; i++) {
    await makeRequest('/api/analytics/track', {
      method: 'POST',
      body: JSON.stringify({
        type: 'search',
        data: {
          queryText: `test query ${i}`,
          resultsCount: i + 1
        }
      })
    });
  }
  
  // Verify counts in database
  const finalViewCount = await queryDatabase('SELECT COUNT(*) FROM company_page_views;');
  const finalSearchCount = await queryDatabase('SELECT COUNT(*) FROM search_queries;');
  
  console.log(`✓ Total company views in database: ${finalViewCount}`);
  console.log(`✓ Total searches in database: ${finalSearchCount}`);
  
  if (parseInt(finalViewCount) < 4) { // 1 from first test + 3 from this test
    console.warn(`Expected at least 4 company views, got ${finalViewCount}`);
  }
  
  if (parseInt(finalSearchCount) < 3) { // 1 from first test + 2 from this test
    console.warn(`Expected at least 3 searches, got ${finalSearchCount}`);
  }
  
  console.log('✓ Data integrity checks completed');
}

// Main test runner
async function runTests() {
  console.log('Starting Simple Analytics System Tests...');
  console.log('==========================================');
  
  try {
    // Setup
    await resetAnalyticsData();
    await insertTestData();
    
    // Run test suites
    await testAnalyticsTracking();
    await testAnalyticsSummaryFunctions();
    await testDataIntegrity();
    
    console.log('\n==========================================');
    console.log('✅ All analytics tests passed!');
    console.log('==========================================');
    
  } catch (error) {
    console.error('\n==========================================');
    console.error('❌ Analytics tests failed:');
    console.error(error.message);
    console.error('==========================================');
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runTests,
  testAnalyticsTracking,
  testAnalyticsSummaryFunctions,
  testDataIntegrity
};
