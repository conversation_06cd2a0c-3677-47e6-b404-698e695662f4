export interface Company {
  id: string
  name: string
  location: string
  size: 'startup' | 'small' | 'medium' | 'large' | 'enterprise'
  industry: string
  description: string | null
  domain: string | null
  career_url: string | null
  created_at: string
  updated_at: string
}

export interface BenefitCategory {
  id: string
  name: string
  display_name: string
  description: string | null
  icon: string | null
  sort_order: number
  is_system: boolean
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Benefit {
  id: string
  name: string
  category: string // Legacy field for backward compatibility
  category_id: string
  icon: string | null
  description: string | null
  created_at: string
  // Populated when joining with benefit_categories
  benefit_category?: BenefitCategory
}

export interface CompanyBenefit {
  id: string
  company_id: string
  benefit_id: string
  added_by: string | null
  is_verified: boolean
  is_admin_verified?: boolean
  created_at: string
  company?: Company
  benefit?: Benefit
}

export interface CompanyUser {
  id: string
  company_id: string
  email: string
  is_verified: boolean
  created_at: string
  company?: Company
}

export interface BenefitVerification {
  id: string
  company_benefit_id: string
  user_id: string
  status: 'confirmed' | 'disputed'
  comment: string | null
  created_at: string
  company_benefit?: CompanyBenefit
}

export interface UserBenefitRanking {
  id: string
  user_id: string
  benefit_id: string
  ranking: number // 1 = most important, 10 = least important
  created_at: string
  updated_at: string
  benefit?: Benefit
}

export interface JobPosting {
  id: string
  company_id: string
  title: string
  description: string
  location: string
  salary_min: number | null
  salary_max: number | null
  employment_type: 'full_time' | 'part_time' | 'contract' | 'internship'
  remote_policy: 'on_site' | 'remote' | 'hybrid'
  is_active: boolean
  created_at: string
  updated_at: string
  company?: Company
}

export type CompanySize = Company['size']
export type BenefitCategoryName = BenefitCategory['name']
export type EmploymentType = JobPosting['employment_type']
export type RemotePolicy = JobPosting['remote_policy']
export type VerificationStatus = BenefitVerification['status']

// Legacy type for backward compatibility
export type LegacyBenefitCategory = 'health' | 'time_off' | 'financial' | 'development' | 'wellness' | 'work_life' | 'other'
