import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}



export function extractDomainFromEmail(email: string): string {
  return email.split('@')[1]?.toLowerCase() || ''
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Selects benefits for display in company overview cards
 * Prioritizes verified benefits and uses random selection for variety
 */
export function selectBenefitsForOverview<T extends { is_verified: boolean }>(
  benefits: T[],
  maxDisplay: number = 5
): T[] {
  // Separate verified and unverified benefits
  const verifiedBenefits = benefits.filter(benefit => benefit.is_verified)
  const unverifiedBenefits = benefits.filter(benefit => !benefit.is_verified)

  // If we have fewer total benefits than maxDisplay, return all with verified first
  if (benefits.length <= maxDisplay) {
    return [...verifiedBenefits, ...unverifiedBenefits]
  }

  // If we have 5+ verified benefits, randomly select 5 verified benefits
  if (verifiedBenefits.length >= maxDisplay) {
    return shuffleArray([...verifiedBenefits]).slice(0, maxDisplay)
  }

  // If we have <5 verified benefits, show all verified + fill remaining with unverified
  const selectedBenefits = [...verifiedBenefits]
  const remainingSlots = maxDisplay - verifiedBenefits.length

  if (remainingSlots > 0 && unverifiedBenefits.length > 0) {
    const shuffledUnverified = shuffleArray([...unverifiedBenefits])
    selectedBenefits.push(...shuffledUnverified.slice(0, remainingSlots))
  }

  return selectedBenefits
}

/**
 * Shuffles an array using Fisher-Yates algorithm
 */
function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}
