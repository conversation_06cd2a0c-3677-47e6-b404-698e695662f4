// Edge-compatible Redis client for middleware
// This is a simplified version that works with Edge Runtime

interface SessionData {
  userId: string
  expiresAt: string
  createdAt: string
}

// In-memory fallback for Edge Runtime (should be replaced with proper Redis in production)
const sessionStore = new Map<string, SessionData>()

export async function getSession(sessionToken: string): Promise<SessionData | null> {
  try {
    // Use in-memory store for Edge Runtime compatibility
    const session = sessionStore.get(`session:${sessionToken}`)
    if (!session) return null

    // Check if expired
    if (new Date(session.expiresAt) < new Date()) {
      sessionStore.delete(`session:${sessionToken}`)
      return null
    }

    return session
  } catch (error) {
    console.error('Error getting session:', error)
    return null
  }
}

export async function setSession(sessionToken: string, userId: string, expiresAt: Date): Promise<boolean> {
  try {
    const sessionData: SessionData = {
      userId,
      expiresAt: expiresAt.toISOString(),
      createdAt: new Date().toISOString(),
    }

    // Store in memory for Edge Runtime compatibility
    sessionStore.set(`session:${sessionToken}`, sessionData)

    // Clean up expired sessions periodically
    setTimeout(() => {
      for (const [key, session] of sessionStore.entries()) {
        if (new Date(session.expiresAt) < new Date()) {
          sessionStore.delete(key)
        }
      }
    }, 60000) // Clean up every minute

    return true
  } catch (error) {
    console.error('Error setting session:', error)
    return false
  }
}

export async function deleteSession(sessionToken: string): Promise<boolean> {
  try {
    sessionStore.delete(`session:${sessionToken}`)
    return true
  } catch (error) {
    console.error('Error deleting session:', error)
    return false
  }
}

// Simple rate limiting for Edge Runtime
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export function checkRateLimit(key: string, limit: number = 100, windowMs: number = 60000): boolean {
  const now = Date.now()
  const record = rateLimitStore.get(key)

  if (!record || now > record.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs })
    return true
  }

  if (record.count >= limit) {
    return false
  }

  record.count++
  return true
}

// CSRF token validation for Edge Runtime
const csrfStore = new Map<string, string>()

export function validateCSRFToken(sessionId: string, providedToken: string): boolean {
  if (!providedToken || !sessionId) {
    return false
  }
  
  const storedToken = csrfStore.get(`csrf:${sessionId}`)
  
  if (!storedToken) {
    return false
  }
  
  // Simple comparison (in production, use timing-safe comparison)
  return storedToken === providedToken
}

export function setCSRFToken(sessionId: string, token: string): void {
  csrfStore.set(`csrf:${sessionId}`, token)
  
  // Clean up expired tokens
  setTimeout(() => {
    csrfStore.delete(`csrf:${sessionId}`)
  }, 3600000) // 1 hour
}
