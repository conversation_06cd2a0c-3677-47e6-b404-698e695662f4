import { query } from '@/lib/local-db'
import { extractDomainFromEmail, isValidEmail } from '@/lib/utils'
import { getCompanyByDomain } from '@/lib/database'
import { sendEmail, EmailOptions } from '@/lib/email'
import { v4 as uuidv4 } from 'uuid'

// Common personal email domains that should not trigger company associations
const PERSONAL_EMAIL_DOMAINS = new Set([
  'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'icloud.com',
  'aol.com', 'protonmail.com', 'tutanota.com', 'gmx.com', 'web.de',
  't-online.de', 'freenet.de', 'arcor.de', 'mail.de'
])

// Rate limiting for verification emails (max 3 per hour per user)
const VERIFICATION_EMAIL_RATE_LIMIT = 3
const VERIFICATION_EMAIL_WINDOW_HOURS = 1

export interface EmailChangeCompanyResult {
  success: boolean
  action: 'no_change' | 'company_updated' | 'company_removed' | 'verification_required'
  previousCompanyId?: string | null
  newCompanyId?: string | null
  companyName?: string
  verificationToken?: string
  message: string
  error?: string
}

export interface CompanyVerificationToken {
  id: string
  token: string
  user_id: string
  user_email: string
  company_id: string
  expires_at: string
  used_at?: string
  created_at: string
}

/**
 * Handles company association changes when a user's email address is updated
 */
export async function handleEmailChangeCompanyAssociation(
  userId: string,
  oldEmail: string,
  newEmail: string,
  firstName?: string
): Promise<EmailChangeCompanyResult> {
  try {
    // Validate email format
    if (!isValidEmail(newEmail)) {
      return {
        success: false,
        action: 'no_change',
        message: 'Invalid email format',
        error: 'Invalid email format'
      }
    }

    const oldDomain = extractDomainFromEmail(oldEmail)
    const newDomain = extractDomainFromEmail(newEmail)

    // If domain hasn't changed, no action needed
    if (oldDomain === newDomain) {
      return {
        success: true,
        action: 'no_change',
        message: 'Email domain unchanged, no company association update needed'
      }
    }

    // Check if new domain is a personal email domain
    if (PERSONAL_EMAIL_DOMAINS.has(newDomain)) {
      // Remove company association if switching to personal email
      const currentUserResult = await query(
        'SELECT company_id FROM users WHERE id = $1',
        [userId]
      )

      if (currentUserResult.rows.length > 0 && currentUserResult.rows[0].company_id) {
        await query(
          'UPDATE users SET company_id = NULL WHERE id = $1',
          [userId]
        )

        return {
          success: true,
          action: 'company_removed',
          previousCompanyId: currentUserResult.rows[0].company_id,
          newCompanyId: null,
          message: 'Company association removed as new email is a personal email address'
        }
      }

      return {
        success: true,
        action: 'no_change',
        message: 'Personal email domain detected, no company association needed'
      }
    }

    // Get current user's company association
    const currentUserResult = await query(
      'SELECT company_id FROM users WHERE id = $1',
      [userId]
    )

    if (currentUserResult.rows.length === 0) {
      return {
        success: false,
        action: 'no_change',
        message: 'User not found',
        error: 'User not found'
      }
    }

    const currentCompanyId = currentUserResult.rows[0].company_id

    // Find company for new email domain
    const newCompany = await getCompanyByDomain(newDomain)

    // Case 1: New domain matches a company
    if (newCompany) {
      // If it's the same company, no change needed
      if (currentCompanyId === newCompany.id) {
        return {
          success: true,
          action: 'no_change',
          previousCompanyId: currentCompanyId,
          newCompanyId: newCompany.id,
          companyName: newCompany.name,
          message: `Email domain still matches ${newCompany.name}, no change needed`
        }
      }

      // Different company - require verification
      // Check rate limiting for verification emails
      const rateLimitCheck = await checkVerificationEmailRateLimit(userId)
      if (!rateLimitCheck.allowed) {
        return {
          success: false,
          action: 'verification_required',
          previousCompanyId: currentCompanyId,
          newCompanyId: newCompany.id,
          companyName: newCompany.name,
          message: `Rate limit exceeded. Please wait before requesting another verification email. ${rateLimitCheck.message}`,
          error: 'Rate limit exceeded'
        }
      }

      const verificationToken = await createCompanyVerificationToken(
        userId,
        newEmail,
        newCompany.id
      )

      // Send verification email
      await sendCompanyVerificationEmail(
        newEmail,
        firstName || 'User',
        newCompany.name,
        verificationToken
      )

      // Record verification email sent for rate limiting
      await recordVerificationEmailSent(userId)

      return {
        success: true,
        action: 'verification_required',
        previousCompanyId: currentCompanyId,
        newCompanyId: newCompany.id,
        companyName: newCompany.name,
        verificationToken,
        message: `Verification email sent for ${newCompany.name} association. Please check your email and click the verification link.`
      }
    }

    // Case 2: New domain doesn't match any company - remove association
    if (currentCompanyId) {
      await query(
        'UPDATE users SET company_id = NULL WHERE id = $1',
        [userId]
      )

      return {
        success: true,
        action: 'company_removed',
        previousCompanyId: currentCompanyId,
        newCompanyId: null,
        message: 'Company association removed as new email domain does not match any registered company'
      }
    }

    // Case 3: No current company and new domain doesn't match - no change
    return {
      success: true,
      action: 'no_change',
      message: 'No company association changes needed'
    }

  } catch (error) {
    console.error('Error handling email change company association:', error)
    return {
      success: false,
      action: 'no_change',
      message: 'Failed to process company association change',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Creates a verification token for company association
 */
export async function createCompanyVerificationToken(
  userId: string,
  userEmail: string,
  companyId: string
): Promise<string> {
  const token = uuidv4()
  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours

  // Delete any existing unused tokens for this user and company
  await query(
    'DELETE FROM company_verification_tokens WHERE user_id = $1 AND company_id = $2 AND used_at IS NULL',
    [userId, companyId]
  )

  // Create new token
  await query(
    `INSERT INTO company_verification_tokens (token, user_id, user_email, company_id, expires_at)
     VALUES ($1, $2, $3, $4, $5)`,
    [token, userId, userEmail.toLowerCase(), companyId, expiresAt]
  )

  return token
}

/**
 * Verifies a company association token and updates user's company
 */
export async function verifyCompanyAssociationToken(token: string): Promise<{
  success: boolean
  userId?: string
  companyId?: string
  companyName?: string
  message: string
  error?: string
}> {
  try {
    // Find and validate token
    const tokenResult = await query(
      `SELECT cvt.*, c.name as company_name
       FROM company_verification_tokens cvt
       JOIN companies c ON cvt.company_id = c.id
       WHERE cvt.token = $1 AND cvt.used_at IS NULL AND cvt.expires_at > NOW()`,
      [token]
    )

    if (tokenResult.rows.length === 0) {
      return {
        success: false,
        message: 'Invalid or expired verification token',
        error: 'Token not found or expired'
      }
    }

    const tokenData = tokenResult.rows[0]

    // Update user's company association
    await query(
      'UPDATE users SET company_id = $1 WHERE id = $2',
      [tokenData.company_id, tokenData.user_id]
    )

    // Mark token as used
    await query(
      'UPDATE company_verification_tokens SET used_at = NOW() WHERE token = $1',
      [token]
    )

    return {
      success: true,
      userId: tokenData.user_id,
      companyId: tokenData.company_id,
      companyName: tokenData.company_name,
      message: `Successfully associated with ${tokenData.company_name}`
    }

  } catch (error) {
    console.error('Error verifying company association token:', error)
    return {
      success: false,
      message: 'Failed to verify company association',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Sends company verification email
 */
export async function sendCompanyVerificationEmail(
  email: string,
  firstName: string,
  companyName: string,
  token: string
): Promise<void> {
  const verificationUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'}/auth/verify-company?token=${token}`

  const emailOptions = createCompanyVerificationEmailContent(
    email,
    firstName,
    companyName,
    verificationUrl
  )

  await sendEmail(emailOptions)
}

/**
 * Creates company verification email content
 */
export function createCompanyVerificationEmailContent(
  email: string,
  firstName: string,
  companyName: string,
  verificationUrl: string
): EmailOptions {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>Verify Your Company Association - WorkWell</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">🏢 Verify Company Association</h1>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
            <h2 style="color: #495057; margin-top: 0;">Hello ${firstName},</h2>
            
            <p style="font-size: 16px; margin-bottom: 20px;">
                You've updated your email address, and we've detected that your new email domain matches <strong>${companyName}</strong>.
            </p>
            
            <p style="font-size: 16px; margin-bottom: 25px;">
                To complete your company association and access company-specific benefits, please verify your email by clicking the button below:
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="${verificationUrl}" 
                   style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                          color: white; 
                          padding: 15px 30px; 
                          text-decoration: none; 
                          border-radius: 25px; 
                          font-weight: bold; 
                          font-size: 16px; 
                          display: inline-block;">
                    ✅ Verify Company Association
                </a>
            </div>
            
            <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 25px 0;">
                <h3 style="color: #1976d2; margin-top: 0;">What happens after verification?</h3>
                <ul style="color: #424242; margin: 0;">
                    <li>Access to ${companyName}'s benefit information</li>
                    <li>Ability to verify and manage company benefits</li>
                    <li>Connect with other ${companyName} employees</li>
                    <li>Company-specific features and content</li>
                </ul>
            </div>
            
            <p style="font-size: 14px; color: #6c757d; margin-top: 25px;">
                This verification link will expire in 24 hours for security reasons.
            </p>
            
            <p style="font-size: 14px; color: #6c757d;">
                If you didn't request this verification or believe this is an error, you can safely ignore this email.
            </p>
            
            <hr style="border: none; border-top: 1px solid #dee2e6; margin: 25px 0;">
            
            <p style="font-size: 12px; color: #868e96; text-align: center;">
                This email was sent to ${email}<br>
                © 2025 WorkWell - Making workplace benefits transparent
            </p>
        </div>
    </body>
    </html>
  `

  const text = `
    Verify Your Company Association - WorkWell
    
    Hello ${firstName},
    
    You've updated your email address, and we've detected that your new email domain matches ${companyName}.
    
    To complete your company association and access company-specific benefits, please verify your email by visiting:
    
    ${verificationUrl}
    
    What happens after verification?
    - Access to ${companyName}'s benefit information
    - Ability to verify and manage company benefits
    - Connect with other ${companyName} employees
    - Company-specific features and content
    
    This verification link will expire in 24 hours for security reasons.
    
    If you didn't request this verification or believe this is an error, you can safely ignore this email.
    
    This email was sent to ${email}
    © 2025 WorkWell - Making workplace benefits transparent
  `

  return {
    to: email,
    subject: `🏢 Verify your association with ${companyName} - WorkWell`,
    html,
    text,
  }
}

/**
 * Checks if user has exceeded verification email rate limit
 */
export async function checkVerificationEmailRateLimit(userId: string): Promise<{
  allowed: boolean
  message: string
  remainingAttempts?: number
}> {
  try {
    const windowStart = new Date(Date.now() - VERIFICATION_EMAIL_WINDOW_HOURS * 60 * 60 * 1000)

    const result = await query(
      `SELECT COUNT(*) as email_count
       FROM company_verification_tokens
       WHERE user_id = $1 AND created_at > $2`,
      [userId, windowStart]
    )

    const emailCount = parseInt(result.rows[0].email_count)

    if (emailCount >= VERIFICATION_EMAIL_RATE_LIMIT) {
      return {
        allowed: false,
        message: `Maximum ${VERIFICATION_EMAIL_RATE_LIMIT} verification emails per hour allowed.`
      }
    }

    return {
      allowed: true,
      message: 'Rate limit check passed',
      remainingAttempts: VERIFICATION_EMAIL_RATE_LIMIT - emailCount
    }
  } catch (error) {
    console.error('Error checking verification email rate limit:', error)
    // Allow on error to avoid blocking legitimate requests
    return {
      allowed: true,
      message: 'Rate limit check failed, allowing request'
    }
  }
}

/**
 * Records that a verification email was sent (for rate limiting)
 */
export async function recordVerificationEmailSent(userId: string): Promise<void> {
  // This is automatically handled by the createCompanyVerificationToken function
  // which inserts a record with the current timestamp
}

/**
 * Validates that a company domain is legitimate for association
 */
export async function validateCompanyDomain(domain: string): Promise<{
  valid: boolean
  reason?: string
}> {
  // Check if it's a personal email domain
  if (PERSONAL_EMAIL_DOMAINS.has(domain)) {
    return {
      valid: false,
      reason: 'Personal email domains are not allowed for company association'
    }
  }

  // Check if domain has proper format
  if (!domain.includes('.') || domain.length < 4) {
    return {
      valid: false,
      reason: 'Invalid domain format'
    }
  }

  // Additional checks could be added here:
  // - Check against known disposable email domains
  // - Verify domain exists via DNS lookup
  // - Check domain reputation

  return {
    valid: true
  }
}

/**
 * Handles multiple domains for a single company
 */
export async function findCompanyByAnyDomain(domain: string) {
  // First try exact match
  let company = await getCompanyByDomain(domain)
  if (company) {
    return company
  }

  // For future enhancement: could check for parent domains or known aliases
  // For example, if company has both "company.com" and "company.de"
  // This would require a company_domains table to store multiple domains per company

  return null
}
