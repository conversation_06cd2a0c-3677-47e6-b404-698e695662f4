import { randomBytes } from 'crypto'
import { cookies } from 'next/headers'
import { setCache, getCache, deleteCache } from './redis'

const CSRF_TOKEN_LENGTH = 32
const CSRF_TOKEN_TTL = 3600 // 1 hour

export function generateCSRFToken(): string {
  return randomBytes(CSRF_TOKEN_LENGTH).toString('hex')
}

export async function setCSRFToken(sessionId: string): Promise<string> {
  const token = generateCSRFToken()
  const key = `csrf:${sessionId}`
  
  // Store in Redis with TTL
  await setCache(key, token, CSRF_TOKEN_TTL)
  
  // Also set as httpOnly cookie
  const cookieStore = await cookies()
  cookieStore.set('csrf_token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: CSRF_TOKEN_TTL,
  })
  
  return token
}

export async function getCSRFToken(sessionId: string): Promise<string | null> {
  const key = `csrf:${sessionId}`
  return await getCache(key)
}

export async function validateCSRFToken(sessionId: string, providedToken: string): Promise<boolean> {
  if (!providedToken || !sessionId) {
    return false
  }
  
  const storedToken = await getCSRFToken(sessionId)
  
  if (!storedToken) {
    return false
  }
  
  // Constant-time comparison to prevent timing attacks
  return timingSafeEqual(Buffer.from(storedToken), Buffer.from(providedToken))
}

export async function deleteCSRFToken(sessionId: string): Promise<void> {
  const key = `csrf:${sessionId}`
  await deleteCache(key)
  
  // Clear cookie
  const cookieStore = await cookies()
  cookieStore.delete('csrf_token')
}

// Timing-safe comparison function
function timingSafeEqual(a: Buffer, b: Buffer): boolean {
  if (a.length !== b.length) {
    return false
  }
  
  let result = 0
  for (let i = 0; i < a.length; i++) {
    result |= a[i] ^ b[i]
  }
  
  return result === 0
}

// Middleware helper to check CSRF for state-changing operations
export function requiresCSRFProtection(method: string, pathname: string): boolean {
  // Protect all non-GET requests to API routes (except auth routes)
  if (pathname.startsWith('/api/') && !pathname.startsWith('/api/auth/')) {
    return ['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)
  }
  
  return false
}

// Extract CSRF token from request
export function extractCSRFToken(request: Request): string | null {
  // Check X-CSRF-Token header first
  const headerToken = request.headers.get('X-CSRF-Token')
  if (headerToken) {
    return headerToken
  }
  
  // For form submissions, check the body (this would need to be implemented in the API route)
  return null
}
