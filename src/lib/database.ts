import { query } from './local-db'
import { supabase, supabaseAdmin } from './supabase'
import { getCache, setCache, clearCachePattern } from './redis'
import type {
  Company,
  Benefit,
  CompanyBenefit,
  CompanyUser,
  BenefitVerification,
  CompanySize,
  BenefitCategory
} from '@/types/database'

// Cache invalidation helpers
async function invalidateCompanyCache(companyId?: string) {
  if (companyId) {
    await clearCachePattern(`company:${companyId}`)
  }
  await clearCachePattern('companies:*')
}

async function invalidateBenefitCache() {
  await clearCachePattern('benefits:*')
}

// Company operations
export async function getCompanies(filters?: {
  location?: string
  size?: CompanySize
  industry?: string[]
  benefits?: string[]
  search?: string
}) {
  let sql = `
    SELECT
      c.*,
      COALESCE(
        json_agg(
          json_build_object(
            'id', cb.id,
            'benefit_id', cb.benefit_id,
            'is_verified', cb.is_verified,
            'benefit', json_build_object(
              'id', b.id,
              'name', b.name,
              'category', b.category,
              'icon', b.icon
            )
          )
        ) FILTER (WHERE cb.id IS NOT NULL),
        '[]'::json
      ) as company_benefits
    FROM companies c
    LEFT JOIN company_benefits cb ON c.id = cb.company_id
    LEFT JOIN benefits b ON cb.benefit_id = b.id
  `

  const conditions: string[] = []
  const params: any[] = []
  let paramIndex = 1

  if (filters?.location) {
    conditions.push(`c.location ILIKE $${paramIndex}`)
    params.push(`%${filters.location}%`)
    paramIndex++
  }

  if (filters?.size) {
    conditions.push(`c.size = $${paramIndex}`)
    params.push(filters.size)
    paramIndex++
  }

  if (filters?.industry && filters.industry.length > 0) {
    const industryConditions = filters.industry.map(() => {
      const condition = `c.industry ILIKE $${paramIndex}`
      paramIndex++
      return condition
    })
    conditions.push(`(${industryConditions.join(' OR ')})`)
    params.push(...filters.industry.map(industry => `%${industry}%`))
  }

  if (filters?.search) {
    conditions.push(`(c.name ILIKE $${paramIndex} OR c.description ILIKE $${paramIndex})`)
    params.push(`%${filters.search}%`)
    paramIndex++
  }

  if (conditions.length > 0) {
    sql += ` WHERE ${conditions.join(' AND ')}`
  }

  sql += ` GROUP BY c.id ORDER BY c.name`

  const result = await query(sql, params)
  let companies = result.rows

  // Filter by benefits if specified
  if (filters?.benefits && filters.benefits.length > 0) {
    companies = companies.filter(company => {
      const companyBenefitNames = company.company_benefits
        ?.map((cb: any) => cb.benefit?.name)
        .filter(Boolean) || []

      return filters.benefits!.some(filterBenefit => {
        // Try exact match first
        if (companyBenefitNames.some((name: string) => name === filterBenefit)) {
          return true
        }
        // Try case-insensitive match
        if (companyBenefitNames.some((name: string) => name.toLowerCase() === filterBenefit.toLowerCase())) {
          return true
        }
        // Try partial match for flexibility
        return companyBenefitNames.some((name: string) =>
          name.toLowerCase().includes(filterBenefit.toLowerCase()) ||
          filterBenefit.toLowerCase().includes(name.toLowerCase())
        )
      })
    })
  }

  return companies
}

export async function getCompanyById(id: string) {
  // Check cache first
  const cacheKey = `company:${id}`
  const cached = await getCache(cacheKey)
  if (cached) {
    return cached
  }

  const sql = `
    SELECT
      c.*,
      COALESCE(
        json_agg(
          json_build_object(
            'id', cb.id,
            'benefit_id', cb.benefit_id,
            'is_verified', cb.is_verified,
            'added_by', cb.added_by,
            'created_at', cb.created_at,
            'benefit', json_build_object(
              'id', b.id,
              'name', b.name,
              'category', b.category,
              'icon', b.icon
            )
          )
        ) FILTER (WHERE cb.id IS NOT NULL),
        '[]'::json
      ) as company_benefits
    FROM companies c
    LEFT JOIN company_benefits cb ON c.id = cb.company_id
    LEFT JOIN benefits b ON cb.benefit_id = b.id
    WHERE c.id = $1
    GROUP BY c.id
  `

  const result = await query(sql, [id])
  const company = result.rows[0] || null

  // Cache for 10 minutes
  if (company) {
    await setCache(cacheKey, company, 600)
  }

  return company
}

export async function createCompany(company: Omit<Company, 'id' | 'created_at' | 'updated_at'>) {
  const result = await query(
    `INSERT INTO companies (name, location, size, industry, description, domain, career_url)
     VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`,
    [company.name, company.location, company.size, company.industry, company.description, company.domain, company.career_url]
  )
  return result.rows[0]
}

export async function updateCompany(id: string, updates: Partial<Company>) {
  const keys = Object.keys(updates).filter(key => updates[key as keyof typeof updates] !== undefined)
  const values = keys.map(key => updates[key as keyof typeof updates])
  const setClause = keys.map((key, i) => `${key} = $${i + 1}`).join(', ')

  const result = await query(
    `UPDATE companies SET ${setClause}, updated_at = NOW() WHERE id = $${keys.length + 1} RETURNING *`,
    [...values, id]
  )
  return result.rows[0]
}

// Benefit operations
export async function getBenefits(category?: string) {
  // Check cache first
  const cacheKey = category ? `benefits:category:${category}` : 'benefits:all'
  const cached = await getCache(cacheKey)
  if (cached) {
    return cached
  }

  let sql = `
    SELECT
      b.*,
      bc.name as category_name,
      bc.display_name as category_display_name,
      bc.icon as category_icon
    FROM benefits b
    LEFT JOIN benefit_categories bc ON b.category_id = bc.id
  `
  const params: any[] = []

  if (category) {
    // Support both legacy category names and new category IDs
    sql += ' WHERE (bc.name = $1 OR b.category_id = $1)'
    params.push(category)
  }

  sql += ' ORDER BY b.name'

  const result = await query(sql, params)

  // Cache for 30 minutes
  await setCache(cacheKey, result.rows, 1800)

  return result.rows
}

export async function createBenefit(benefit: Omit<Benefit, 'id' | 'created_at'>) {
  const result = await query(
    'INSERT INTO benefits (name, category, category_id, icon) VALUES ($1, $2, $3, $4) RETURNING *',
    [benefit.name, benefit.category, benefit.category_id, benefit.icon]
  )
  return result.rows[0]
}

// Benefit category operations
export async function getBenefitCategories(includeInactive = false) {
  let sql = 'SELECT * FROM benefit_categories'

  if (!includeInactive) {
    sql += ' WHERE is_active = true'
  }

  sql += ' ORDER BY sort_order ASC, display_name ASC'

  const result = await query(sql)
  return result.rows
}

export async function createBenefitCategory(category: {
  name: string
  display_name: string
  description?: string
  icon?: string
  sort_order?: number
}) {
  const result = await query(
    `INSERT INTO benefit_categories (name, display_name, description, icon, sort_order, is_system, is_active)
     VALUES ($1, $2, $3, $4, $5, false, true) RETURNING *`,
    [category.name, category.display_name, category.description, category.icon, category.sort_order || 0]
  )
  return result.rows[0]
}

// Company benefit operations
export async function addCompanyBenefit(companyId: string, benefitId: string, addedBy?: string) {
  const result = await query(
    `INSERT INTO company_benefits (company_id, benefit_id, added_by, is_verified)
     VALUES ($1, $2, $3, $4) RETURNING *`,
    [companyId, benefitId, addedBy, false]
  )
  return result.rows[0]
}

export async function removeCompanyBenefit(companyId: string, benefitId: string) {
  await query(
    'DELETE FROM company_benefits WHERE company_id = $1 AND benefit_id = $2',
    [companyId, benefitId]
  )
}

export async function verifyCompanyBenefit(companyBenefitId: string, isVerified: boolean) {
  const result = await query(
    'UPDATE company_benefits SET is_verified = $1 WHERE id = $2 RETURNING *',
    [isVerified, companyBenefitId]
  )
  return result.rows[0]
}

export async function getCompanyBenefits(companyId: string) {
  const result = await query(
    `SELECT
       cb.*,
       b.name,
       b.category,
       b.icon,
       CASE
         WHEN cb.added_by IS NULL OR cb.added_by = 'system' OR cb.added_by LIKE '%admin%'
         THEN true
         ELSE false
       END as is_admin_verified
     FROM company_benefits cb
     JOIN benefits b ON cb.benefit_id = b.id
     WHERE cb.company_id = $1
     ORDER BY b.name`,
    [companyId]
  )
  return result.rows
}

export async function bulkAddCompanyBenefits(companyId: string, benefitIds: string[], addedBy?: string) {
  const values = benefitIds.map((benefitId, index) =>
    `($1, $${index + 2}, $${benefitIds.length + 2}, false)`
  ).join(', ')

  const params = [companyId, ...benefitIds, addedBy]

  const result = await query(
    `INSERT INTO company_benefits (company_id, benefit_id, added_by, is_verified)
     VALUES ${values}
     ON CONFLICT (company_id, benefit_id) DO NOTHING
     RETURNING *`,
    params
  )
  return result.rows
}

export async function bulkRemoveCompanyBenefits(companyId: string, benefitIds: string[]) {
  const placeholders = benefitIds.map((_, index) => `$${index + 2}`).join(', ')

  await query(
    `DELETE FROM company_benefits
     WHERE company_id = $1 AND benefit_id IN (${placeholders})`,
    [companyId, ...benefitIds]
  )
}

export async function updateCompanyBenefit(companyBenefitId: string, updates: {
  is_verified?: boolean
  added_by?: string
}) {
  const keys = Object.keys(updates).filter(key => updates[key as keyof typeof updates] !== undefined)
  const values = keys.map(key => updates[key as keyof typeof updates])
  const setClause = keys.map((key, i) => `${key} = $${i + 1}`).join(', ')

  const result = await query(
    `UPDATE company_benefits SET ${setClause} WHERE id = $${keys.length + 1} RETURNING *`,
    [...values, companyBenefitId]
  )
  return result.rows[0]
}



// Search functions
export async function searchCompanies(searchQuery: string) {
  const sql = `
    SELECT
      c.*,
      COALESCE(
        json_agg(
          json_build_object(
            'id', cb.id,
            'benefit_id', cb.benefit_id,
            'is_verified', cb.is_verified,
            'benefit', json_build_object(
              'id', b.id,
              'name', b.name,
              'category', b.category,
              'icon', b.icon
            )
          )
        ) FILTER (WHERE cb.id IS NOT NULL),
        '[]'::json
      ) as company_benefits
    FROM companies c
    LEFT JOIN company_benefits cb ON c.id = cb.company_id
    LEFT JOIN benefits b ON cb.benefit_id = b.id
    WHERE c.name ILIKE $1 OR c.description ILIKE $1 OR c.location ILIKE $1
    GROUP BY c.id
    ORDER BY c.name
    LIMIT 20
  `

  const result = await query(sql, [`%${searchQuery}%`])
  return result.rows
}

export async function getCompanyByDomain(domain: string) {
  const result = await query(
    'SELECT * FROM companies WHERE domain = $1',
    [domain.toLowerCase()]
  )
  return result.rows[0] || null
}
