// Structured logging utility
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug'
}

export interface LogContext {
  userId?: string
  sessionId?: string
  requestId?: string
  ip?: string
  userAgent?: string
  method?: string
  url?: string
  statusCode?: number
  responseTime?: number
  error?: Error | string
  [key: string]: any
}

export interface LogEntry {
  timestamp: string
  level: LogLevel
  message: string
  context?: LogContext
  stack?: string
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development'
  private logLevel = process.env.LOG_LEVEL || (this.isDevelopment ? 'debug' : 'info')

  private shouldLog(level: LogLevel): boolean {
    const levels = [LogLevel.ERROR, LogLevel.WARN, LogLevel.INFO, LogLevel.DEBUG]
    const currentLevelIndex = levels.indexOf(this.logLevel as LogLevel)
    const messageLevelIndex = levels.indexOf(level)
    
    return messageLevelIndex <= currentLevelIndex
  }

  private formatLog(level: LogLevel, message: string, context?: LogContext): LogEntry {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context
    }

    // Add stack trace for errors
    if (context?.error instanceof Error) {
      entry.stack = context.error.stack
    }

    return entry
  }

  private output(entry: LogEntry): void {
    if (this.isDevelopment) {
      // Pretty print for development
      const colorMap = {
        [LogLevel.ERROR]: '\x1b[31m', // Red
        [LogLevel.WARN]: '\x1b[33m',  // Yellow
        [LogLevel.INFO]: '\x1b[36m',  // Cyan
        [LogLevel.DEBUG]: '\x1b[37m'  // White
      }
      
      const reset = '\x1b[0m'
      const color = colorMap[entry.level]
      
      console.log(
        `${color}[${entry.timestamp}] ${entry.level.toUpperCase()}${reset}: ${entry.message}`
      )
      
      if (entry.context) {
        console.log('Context:', JSON.stringify(entry.context, null, 2))
      }
      
      if (entry.stack) {
        console.log('Stack:', entry.stack)
      }
    } else {
      // JSON format for production (easier for log aggregation)
      console.log(JSON.stringify(entry))
    }
  }

  error(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      this.output(this.formatLog(LogLevel.ERROR, message, context))
    }
  }

  warn(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.WARN)) {
      this.output(this.formatLog(LogLevel.WARN, message, context))
    }
  }

  info(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.INFO)) {
      this.output(this.formatLog(LogLevel.INFO, message, context))
    }
  }

  debug(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      this.output(this.formatLog(LogLevel.DEBUG, message, context))
    }
  }

  // Convenience methods for common scenarios
  apiRequest(method: string, url: string, context?: Omit<LogContext, 'method' | 'url'>): void {
    this.info('API Request', { method, url, ...context })
  }

  apiResponse(method: string, url: string, statusCode: number, responseTime: number, context?: LogContext): void {
    const level = statusCode >= 500 ? LogLevel.ERROR : 
                  statusCode >= 400 ? LogLevel.WARN : 
                  LogLevel.INFO
    
    this[level]('API Response', { 
      method, 
      url, 
      statusCode, 
      responseTime,
      ...context 
    })
  }

  databaseQuery(query: string, duration: number, context?: LogContext): void {
    this.debug('Database Query', { 
      query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
      duration,
      ...context 
    })
  }

  databaseError(query: string, error: Error, context?: LogContext): void {
    this.error('Database Error', { 
      query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
      error,
      ...context 
    })
  }

  authEvent(event: string, userId?: string, context?: LogContext): void {
    this.info('Auth Event', { 
      event, 
      userId,
      ...context 
    })
  }

  securityEvent(event: string, severity: 'low' | 'medium' | 'high' | 'critical', context?: LogContext): void {
    const level = severity === 'critical' || severity === 'high' ? LogLevel.ERROR : LogLevel.WARN
    
    this[level]('Security Event', { 
      event, 
      severity,
      ...context 
    })
  }

  performanceMetric(metric: string, value: number, unit: string, context?: LogContext): void {
    this.info('Performance Metric', { 
      metric, 
      value, 
      unit,
      ...context 
    })
  }
}

// Export singleton instance
export const logger = new Logger()

// Helper function to extract request context
export function getRequestContext(request: Request): LogContext {
  const url = new URL(request.url)
  
  return {
    method: request.method,
    url: url.pathname + url.search,
    ip: request.headers.get('x-forwarded-for')?.split(',')[0] || 
        request.headers.get('x-real-ip') || 
        'unknown',
    userAgent: request.headers.get('user-agent') || 'unknown'
  }
}

// Middleware helper to log requests
export function logRequest(request: Request, startTime: number = Date.now()): LogContext {
  const context = getRequestContext(request)
  logger.apiRequest(context.method!, context.url!, context)
  
  return { ...context, startTime }
}

// Middleware helper to log responses
export function logResponse(
  request: Request, 
  response: Response, 
  startTime: number,
  additionalContext?: LogContext
): void {
  const context = getRequestContext(request)
  const responseTime = Date.now() - startTime
  
  logger.apiResponse(
    context.method!,
    context.url!,
    response.status,
    responseTime,
    { ...context, ...additionalContext }
  )
}
