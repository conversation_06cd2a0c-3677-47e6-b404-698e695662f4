import { query } from './local-db'

// Create a local database client that mimics Supabase API
const createLocalClient = () => ({
  from: (table: string) => ({
    select: (columns = '*') => {
      const conditions: Array<{ column: string, value: any }> = []

      const buildQuery = () => {
        if (conditions.length === 0) {
          return { sql: `SELECT ${columns} FROM ${table}`, params: [] }
        }
        const whereClause = conditions.map((_, i) => `${conditions[i].column} = $${i + 1}`).join(' AND ')
        const params = conditions.map(c => c.value)
        return { sql: `SELECT ${columns} FROM ${table} WHERE ${whereClause}`, params }
      }

      const queryBuilder: any = {
        single: async () => {
          try {
            const { sql, params } = buildQuery()
            const result = await query(sql + ' LIMIT 1', params)
            return { data: result.rows[0] || null, error: null }
          } catch (error) {
            return { data: null, error }
          }
        },
        then: async (resolve: any) => {
          try {
            const { sql, params } = buildQuery()
            const result = await query(sql, params)
            return resolve({ data: result.rows, error: null })
          } catch (error) {
            return resolve({ data: null, error })
          }
        }
      }

      // Add eq method that supports chaining
      queryBuilder.eq = (column: string, value: any) => {
        conditions.push({ column, value })
        return queryBuilder
      }

      // Add other methods
      queryBuilder.ilike = (column: string, value: string) => ({
        async then(resolve: any) {
          try {
            const result = await query(
              `SELECT ${columns} FROM ${table} WHERE ${column} ILIKE $1`,
              [value]
            )
            return resolve({ data: result.rows, error: null })
          } catch (error) {
            return resolve({ data: null, error })
          }
        }
      })

      queryBuilder.or = (condition: string) => ({
        async then(resolve: any) {
          try {
            // Simple OR condition parsing for search
            const searchTerm = condition.match(/%([^%]+)%/)?.[1] || ''
            const result = await query(
              `SELECT ${columns} FROM ${table} WHERE name ILIKE $1 OR description ILIKE $1`,
              [`%${searchTerm}%`]
            )
            return resolve({ data: result.rows, error: null })
          } catch (error) {
            return resolve({ data: null, error })
          }
        }
      })

      queryBuilder.limit = (count: number) => ({
        async then(resolve: any) {
          try {
            const result = await query(
              `SELECT ${columns} FROM ${table} LIMIT $1`,
              [count]
            )
            return resolve({ data: result.rows, error: null })
          } catch (error) {
            return resolve({ data: null, error })
          }
        }
      })

      queryBuilder.order = (column: string) => ({
        async then(resolve: any) {
          try {
            const result = await query(
              `SELECT ${columns} FROM ${table} ORDER BY ${column}`
            )
            return resolve({ data: result.rows, error: null })
          } catch (error) {
            return resolve({ data: null, error })
          }
        }
      })

      return queryBuilder
    },
    insert: (data: any) => ({
      select: () => ({
        single: async () => {
          try {
            const keys = Object.keys(data)
            const values = Object.values(data)
            const placeholders = keys.map((_, i) => `$${i + 1}`).join(', ')
            const result = await query(
              `INSERT INTO ${table} (${keys.join(', ')}) VALUES (${placeholders}) RETURNING *`,
              values
            )
            return { data: result.rows[0], error: null }
          } catch (error) {
            return { data: null, error }
          }
        }
      })
    }),
    update: (data: any) => ({
      eq: (column: string, value: any) => ({
        select: () => ({
          single: async () => {
            try {
              const keys = Object.keys(data)
              const values = Object.values(data)
              const setClause = keys.map((key, i) => `${key} = $${i + 1}`).join(', ')
              const result = await query(
                `UPDATE ${table} SET ${setClause} WHERE ${column} = $${keys.length + 1} RETURNING *`,
                [...values, value]
              )
              return { data: result.rows[0], error: null }
            } catch (error) {
              return { data: null, error }
            }
          }
        })
      })
    }),
    delete: () => ({
      eq: (column: string, value: any) => ({
        async then(resolve: any) {
          try {
            await query(`DELETE FROM ${table} WHERE ${column} = $1`, [value])
            return resolve({ error: null })
          } catch (error) {
            return resolve({ error })
          }
        }
      })
    })
  })
})

// Use local client for all environments (self-hosted)
export const supabase = createLocalClient() as any
export const supabaseAdmin = createLocalClient() as any
