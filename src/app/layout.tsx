import type { Metada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

// Initialize performance monitoring and environment validation
import { startPerformanceMonitoring } from "@/lib/performance";
import { validateEnvironment } from "@/lib/env-validation";

// Initialize systems on server startup
if (typeof window === 'undefined') {
  // Validate environment
  validateEnvironment();

  // Start performance monitoring
  startPerformanceMonitoring();
}

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Workwell - Compare Companies by Benefits",
  description: "Find and compare companies based on their employee benefits. Search by location, benefits, and more.",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
