import { NextRequest, NextResponse } from 'next/server'
import { getCompanies, createCompany } from '@/lib/database'
import { requireAuth, getCurrentUser } from '@/lib/auth'
import { logCompanyAdded } from '@/lib/activity-logger'
import type { CompanySize } from '@/types/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const filters = {
      location: searchParams.get('location') || undefined,
      size: (searchParams.get('size') as CompanySize) || undefined,
      industry: searchParams.get('industry')?.split(',').filter(Boolean) || undefined,
      benefits: searchParams.get('benefits')?.split(',').filter(Boolean) || undefined,
      search: searchParams.get('search') || undefined,
    }

    const companies = await getCompanies(filters)
    
    return NextResponse.json(companies)
  } catch (error) {
    console.error('Error fetching companies:', error)
    return NextResponse.json(
      { error: 'Failed to fetch companies' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth()

    const body = await request.json()
    const { name, location, size, industry, description, domain, career_url } = body

    if (!name || !location || !size || !industry) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const company = await createCompany({
      name,
      location,
      size,
      industry,
      description,
      domain: domain?.toLowerCase(),
      career_url: career_url || '',
    })

    // Get user details for activity logging
    const currentUser = await getCurrentUser()

    // Log the company addition activity
    await logCompanyAdded(
      company.id,
      company.name,
      currentUser?.id,
      currentUser?.email,
      `${currentUser?.firstName || ''} ${currentUser?.lastName || ''}`.trim() || undefined
    )

    return NextResponse.json(company, { status: 201 })
  } catch (error) {
    console.error('Error creating company:', error)
    return NextResponse.json(
      { error: 'Failed to create company' },
      { status: 500 }
    )
  }
}
