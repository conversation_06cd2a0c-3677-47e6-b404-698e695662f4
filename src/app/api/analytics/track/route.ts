import { NextRequest, NextResponse } from 'next/server'
import { trackCompanyView, trackSearch, trackBenefitInteraction } from '@/lib/analytics-tracker'

// POST /api/analytics/track - Track analytics events
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, data } = body

    if (!type || !data) {
      return NextResponse.json(
        { error: 'Missing type or data in request body' },
        { status: 400 }
      )
    }

    switch (type) {
      case 'company_view':
        if (!data.companyId) {
          return NextResponse.json(
            { error: 'Missing companyId for company_view event' },
            { status: 400 }
          )
        }
        await trackCompanyView(data.companyId, data.referrer)
        break

      case 'search':
        if (!data.queryText || data.resultsCount === undefined) {
          return NextResponse.json(
            { error: 'Missing queryText or resultsCount for search event' },
            { status: 400 }
          )
        }
        const searchId = await trackSearch(data.queryText, data.resultsCount, data.filtersApplied)
        return NextResponse.json({ success: true, searchId })

      case 'benefit_interaction':
        if (!data.benefitId || !data.companyId || !data.interactionType) {
          return NextResponse.json(
            { error: 'Missing benefitId, companyId, or interactionType for benefit_interaction event' },
            { status: 400 }
          )
        }
        await trackBenefitInteraction(
          data.benefitId,
          data.companyId,
          data.interactionType,
          data.searchQueryId
        )
        break

      default:
        return NextResponse.json(
          { error: `Unknown event type: ${type}` },
          { status: 400 }
        )
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error tracking analytics event:', error)
    return NextResponse.json(
      { error: 'Failed to track analytics event' },
      { status: 500 }
    )
  }
}
