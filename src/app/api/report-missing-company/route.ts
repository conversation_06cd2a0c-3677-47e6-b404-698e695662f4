import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser, requireAuth } from '@/lib/auth'
import { sendEmail } from '@/lib/email'
import { query } from '@/lib/local-db'

export async function POST(request: NextRequest) {
  try {
    await requireAuth()
    const user = await getCurrentUser()
    
    if (!user?.email) {
      return NextResponse.json(
        { error: 'User email not found' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { userEmail, emailDomain, firstName, lastName } = body

    if (!userEmail || !emailDomain) {
      return NextResponse.json(
        { error: 'User email and email domain are required' },
        { status: 400 }
      )
    }

    // Verify the user email matches the authenticated user
    if (userEmail.toLowerCase() !== user.email.toLowerCase()) {
      return NextResponse.json(
        { error: 'Email mismatch' },
        { status: 400 }
      )
    }

    // Check if there's already a recent report from this user for the same email domain
    const recentReportResult = await query(
      'SELECT id FROM missing_company_reports WHERE user_email = $1 AND email_domain = $2 AND created_at > NOW() - INTERVAL \'24 hours\'',
      [userEmail.toLowerCase(), emailDomain.toLowerCase()]
    )

    if (recentReportResult.rows.length > 0) {
      return NextResponse.json(
        { error: 'You have already submitted a report for this company domain in the last 24 hours. Please wait before submitting another report.' },
        { status: 429 }
      )
    }

    // Store the missing company report
    const reportResult = await query(
      `INSERT INTO missing_company_reports (user_email, email_domain, first_name, last_name, status, created_at)
       VALUES ($1, $2, $3, $4, 'pending', NOW()) RETURNING id`,
      [userEmail.toLowerCase(), emailDomain.toLowerCase(), firstName || null, lastName || null]
    )

    const reportId = reportResult.rows[0].id

    // Send notification email to admin
    const adminEmailOptions = createAdminNotificationEmail({
      reportId,
      userEmail,
      emailDomain,
      firstName,
      lastName
    })

    await sendEmail(adminEmailOptions)

    console.log('✅ Missing company report submitted:', { reportId, userEmail, emailDomain })

    return NextResponse.json({
      success: true,
      message: 'Thank you! We have received your report and will review it shortly. You will be notified via email once your company is added.',
      reportId
    })

  } catch (error) {
    console.error('Error submitting missing company report:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to submit report' },
      { status: 500 }
    )
  }
}

interface AdminNotificationData {
  reportId: string
  userEmail: string
  emailDomain: string
  firstName?: string
  lastName?: string
}

function createAdminNotificationEmail(data: AdminNotificationData) {
  const { reportId, userEmail, emailDomain, firstName, lastName } = data
  const userName = firstName && lastName ? `${firstName} ${lastName}` : firstName || lastName || 'User'

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Missing Company Report - WorkWell</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #2563eb; color: white; padding: 20px; text-align: center; }
        .content { background-color: #f9fafb; padding: 20px; }
        .info-box { background-color: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px; margin: 15px 0; }
        .footer { text-align: center; padding: 20px; font-size: 12px; color: #6b7280; }
        .highlight { background-color: #fef3c7; padding: 2px 4px; border-radius: 4px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🏢 Missing Company Report</h1>
          <p>A user has reported a missing company on WorkWell</p>
        </div>
        
        <div class="content">
          <div class="info-box">
            <h3>Report Details</h3>
            <p><strong>Report ID:</strong> ${reportId}</p>
            <p><strong>User Name:</strong> ${userName}</p>
            <p><strong>User Email:</strong> ${userEmail}</p>
            <p><strong>Email Domain:</strong> <span class="highlight">${emailDomain}</span></p>
            <p><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
          </div>

          <div class="info-box">
            <h3>Next Steps</h3>
            <ol>
              <li>Research the company associated with domain: <strong>${emailDomain}</strong></li>
              <li>Add the company to the WorkWell platform if legitimate</li>
              <li>Notify the user once the company is added</li>
            </ol>
          </div>

          <div class="info-box">
            <h3>Quick Actions</h3>
            <p>You can manage this report and add the company through the WorkWell admin dashboard.</p>
            <p><a href="${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/admin" style="color: #2563eb;">Go to Admin Dashboard</a></p>
          </div>
        </div>
        
        <div class="footer">
          <p>This notification was <NAME_EMAIL></p>
          <p>WorkWell - Company Benefits Verification Platform</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
    WorkWell - Missing Company Report
    
    A user has reported a missing company on WorkWell.
    
    Report Details:
    - Report ID: ${reportId}
    - User Name: ${userName}
    - User Email: ${userEmail}
    - Email Domain: ${emailDomain}
    - Submitted: ${new Date().toLocaleString()}
    
    Next Steps:
    1. Research the company associated with domain: ${emailDomain}
    2. Add the company to the WorkWell platform if legitimate
    3. Notify the user once the company is added
    
    You can manage this report through the WorkWell admin dashboard:
    ${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/admin
    
    This notification was <NAME_EMAIL>
  `

  return {
    to: '<EMAIL>',
    subject: `🏢 Missing Company Report: ${emailDomain}`,
    html,
    text,
  }
}
