import { NextResponse } from 'next/server'
import { query } from '@/lib/local-db'

export async function GET() {
  try {
    // Get all benefits with count of companies offering each benefit
    const sql = `
      SELECT 
        b.name,
        b.category,
        b.icon,
        COUNT(DISTINCT cb.company_id) as company_count
      FROM benefits b
      LEFT JOIN company_benefits cb ON b.id = cb.benefit_id
      GROUP BY b.id, b.name, b.category, b.icon
      ORDER BY company_count DESC, b.name ASC
    `

    const result = await query(sql)
    
    const benefits = result.rows.map(row => ({
      value: row.name,
      label: row.name,
      count: parseInt(row.company_count),
      category: row.category,
      icon: row.icon
    }))

    return NextResponse.json(benefits)
  } catch (error) {
    console.error('Error fetching benefit options:', error)
    return NextResponse.json(
      { error: 'Failed to fetch benefit options' },
      { status: 500 }
    )
  }
}
