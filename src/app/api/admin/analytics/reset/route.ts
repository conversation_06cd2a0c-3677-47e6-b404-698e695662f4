import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'

// POST /api/admin/analytics/reset - Reset analytics data
export async function POST(request: NextRequest) {
  try {
    await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const resetType = searchParams.get('type') || 'all' // all, company_views, searches, interactions, summaries
    const companyId = searchParams.get('companyId') // Optional: reset for specific company
    
    let resetCount = 0
    let resetDetails: string[] = []
    
    if (resetType === 'all' || resetType === 'company_views') {
      if (companyId) {
        const result = await query('DELETE FROM company_page_views WHERE company_id = $1', [companyId])
        resetCount += result.rowCount || 0
        resetDetails.push(`${result.rowCount || 0} company page views for company ${companyId}`)
      } else {
        const result = await query('DELETE FROM company_page_views')
        resetCount += result.rowCount || 0
        resetDetails.push(`${result.rowCount || 0} company page views`)
      }
    }
    
    if (resetType === 'all' || resetType === 'searches') {
      if (!companyId) { // Search queries are not company-specific
        const result = await query('DELETE FROM search_queries')
        resetCount += result.rowCount || 0
        resetDetails.push(`${result.rowCount || 0} search queries`)
      }
    }
    
    if (resetType === 'all' || resetType === 'interactions') {
      if (companyId) {
        const result = await query('DELETE FROM benefit_search_interactions WHERE company_id = $1', [companyId])
        resetCount += result.rowCount || 0
        resetDetails.push(`${result.rowCount || 0} benefit interactions for company ${companyId}`)
      } else {
        const result = await query('DELETE FROM benefit_search_interactions')
        resetCount += result.rowCount || 0
        resetDetails.push(`${result.rowCount || 0} benefit interactions`)
      }
    }
    
    if (resetType === 'all' || resetType === 'summaries') {
      if (companyId) {
        const result = await query('DELETE FROM company_analytics_summary WHERE company_id = $1', [companyId])
        resetCount += result.rowCount || 0
        resetDetails.push(`${result.rowCount || 0} company analytics summaries for company ${companyId}`)
      } else {
        // Reset both daily and company summaries
        const dailyResult = await query('DELETE FROM daily_analytics_summary')
        const companyResult = await query('DELETE FROM company_analytics_summary')
        resetCount += (dailyResult.rowCount || 0) + (companyResult.rowCount || 0)
        resetDetails.push(`${dailyResult.rowCount || 0} daily analytics summaries`)
        resetDetails.push(`${companyResult.rowCount || 0} company analytics summaries`)
      }
    }
    
    // Log the reset action
    const resetMessage = companyId 
      ? `Analytics reset for company ${companyId} (type: ${resetType})`
      : `Analytics reset (type: ${resetType})`
    
    return NextResponse.json({
      success: true,
      message: resetMessage,
      resetCount,
      resetDetails,
      resetType,
      companyId: companyId || null,
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('Error resetting analytics:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to reset analytics' },
      { status: 500 }
    )
  }
}

// GET /api/admin/analytics/reset - Get analytics reset options and current data counts
export async function GET(request: NextRequest) {
  try {
    await requireAdmin()
    
    // Get current data counts
    const [
      companyViewsResult,
      searchQueriesResult,
      interactionsResult,
      dailySummariesResult,
      companySummariesResult
    ] = await Promise.all([
      query('SELECT COUNT(*) as count FROM company_page_views'),
      query('SELECT COUNT(*) as count FROM search_queries'),
      query('SELECT COUNT(*) as count FROM benefit_search_interactions'),
      query('SELECT COUNT(*) as count FROM daily_analytics_summary'),
      query('SELECT COUNT(*) as count FROM company_analytics_summary')
    ])
    
    // Get companies with analytics data
    const companiesWithDataResult = await query(`
      SELECT DISTINCT 
        c.id,
        c.name,
        (SELECT COUNT(*) FROM company_page_views cpv WHERE cpv.company_id = c.id) as page_views,
        (SELECT COUNT(*) FROM benefit_search_interactions bsi WHERE bsi.company_id = c.id) as interactions,
        (SELECT COUNT(*) FROM company_analytics_summary cas WHERE cas.company_id = c.id) as summary_records
      FROM companies c
      WHERE EXISTS (
        SELECT 1 FROM company_page_views cpv WHERE cpv.company_id = c.id
      ) OR EXISTS (
        SELECT 1 FROM benefit_search_interactions bsi WHERE bsi.company_id = c.id
      ) OR EXISTS (
        SELECT 1 FROM company_analytics_summary cas WHERE cas.company_id = c.id
      )
      ORDER BY c.name
    `)
    
    return NextResponse.json({
      currentCounts: {
        company_page_views: parseInt(companyViewsResult.rows[0]?.count || 0),
        search_queries: parseInt(searchQueriesResult.rows[0]?.count || 0),
        benefit_interactions: parseInt(interactionsResult.rows[0]?.count || 0),
        daily_summaries: parseInt(dailySummariesResult.rows[0]?.count || 0),
        company_summaries: parseInt(companySummariesResult.rows[0]?.count || 0)
      },
      companiesWithData: companiesWithDataResult.rows.map(row => ({
        id: row.id,
        name: row.name,
        page_views: parseInt(row.page_views),
        interactions: parseInt(row.interactions),
        summary_records: parseInt(row.summary_records)
      })),
      resetOptions: [
        { value: 'all', label: 'All Analytics Data', description: 'Reset all analytics tracking data' },
        { value: 'company_views', label: 'Company Page Views', description: 'Reset only company page view tracking' },
        { value: 'searches', label: 'Search Queries', description: 'Reset only search query tracking' },
        { value: 'interactions', label: 'Benefit Interactions', description: 'Reset only benefit interaction tracking' },
        { value: 'summaries', label: 'Analytics Summaries', description: 'Reset only aggregated summary data' }
      ]
    })
    
  } catch (error) {
    console.error('Error getting analytics reset info:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to get analytics reset info' },
      { status: 500 }
    )
  }
}
