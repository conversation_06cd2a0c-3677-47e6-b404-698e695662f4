import { NextRequest, NextResponse } from 'next/server'
import { deleteSession } from '@/lib/local-auth'

export async function POST(request: NextRequest) {
  try {
    await deleteSession()
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Sign out error:', error)
    return NextResponse.json(
      { error: 'Failed to sign out' },
      { status: 500 }
    )
  }
}
