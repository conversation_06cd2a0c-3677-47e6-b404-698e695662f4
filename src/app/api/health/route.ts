import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { checkRedisHealth } from '@/lib/redis'
import * as v8 from 'v8'

interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded'
  timestamp: string
  uptime: number
  version: string
  services: {
    database: ServiceHealth
    redis: ServiceHealth
    application: ServiceHealth
  }
  metrics?: {
    memory: {
      used: number
      total: number
      percentage: number
    }
    responseTime: number
  }
}

interface ServiceHealth {
  status: 'healthy' | 'unhealthy' | 'degraded'
  responseTime?: number
  error?: string
  details?: any
}

async function checkDatabaseHealth(): Promise<ServiceHealth> {
  const startTime = Date.now()
  
  try {
    // Test basic connectivity
    await query('SELECT 1 as health_check')
    
    // Test a more complex query to ensure database is functioning properly
    const result = await query(`
      SELECT 
        COUNT(*) as company_count,
        (SELECT COUNT(*) FROM benefits) as benefit_count,
        (SELECT COUNT(*) FROM users) as user_count
      FROM companies
    `)
    
    const responseTime = Date.now() - startTime
    
    return {
      status: responseTime < 1000 ? 'healthy' : 'degraded',
      responseTime,
      details: {
        companies: result.rows[0]?.company_count || 0,
        benefits: result.rows[0]?.benefit_count || 0,
        users: result.rows[0]?.user_count || 0,
      }
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown database error'
    }
  }
}

async function checkRedisHealthStatus(): Promise<ServiceHealth> {
  const startTime = Date.now()
  
  try {
    const isHealthy = await checkRedisHealth()
    const responseTime = Date.now() - startTime
    
    if (!isHealthy) {
      return {
        status: 'unhealthy',
        responseTime,
        error: 'Redis ping failed'
      }
    }
    
    return {
      status: responseTime < 500 ? 'healthy' : 'degraded',
      responseTime
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown Redis error'
    }
  }
}

function checkApplicationHealth(): ServiceHealth {
  const startTime = Date.now()

  try {
    // Check memory usage
    const memUsage = process.memoryUsage()
    const memoryUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024)
    const memoryAllocatedMB = Math.round(memUsage.heapTotal / 1024 / 1024)
    const memoryRssMB = Math.round(memUsage.rss / 1024 / 1024)

    // Get max heap size from V8 flags or use default
    const heapStats = v8.getHeapStatistics()
    const maxHeapSizeMB = Math.round(heapStats.heap_size_limit / 1024 / 1024)

    // Calculate percentage against maximum possible heap size, not currently allocated
    const memoryPercentageOfMax = (memUsage.heapUsed / heapStats.heap_size_limit) * 100
    const memoryPercentageOfAllocated = (memUsage.heapUsed / memUsage.heapTotal) * 100

    // Consider degraded if using more than 80% of max heap size OR more than 95% of allocated heap
    const isMemoryHealthy = memoryPercentageOfMax < 80 && memoryPercentageOfAllocated < 95

    const responseTime = Date.now() - startTime

    return {
      status: isMemoryHealthy ? 'healthy' : 'degraded',
      responseTime,
      details: {
        memory: {
          used: memoryUsedMB,
          allocated: memoryAllocatedMB,
          maxHeapSize: maxHeapSizeMB,
          rss: memoryRssMB,
          percentageOfMax: Math.round(memoryPercentageOfMax),
          percentageOfAllocated: Math.round(memoryPercentageOfAllocated)
        },
        uptime: process.uptime(),
        nodeVersion: process.version,
        platform: process.platform
      }
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown application error'
    }
  }
}

export async function GET(_request: NextRequest) {
  const startTime = Date.now()
  
  try {
    // Run all health checks in parallel
    const [databaseHealth, redisHealth, applicationHealth] = await Promise.all([
      checkDatabaseHealth(),
      checkRedisHealthStatus(),
      checkApplicationHealth()
    ])
    
    // Determine overall status
    const services = { database: databaseHealth, redis: redisHealth, application: applicationHealth }
    const serviceStatuses = Object.values(services).map(s => s.status)
    
    let overallStatus: 'healthy' | 'unhealthy' | 'degraded'
    if (serviceStatuses.includes('unhealthy')) {
      overallStatus = 'unhealthy'
    } else if (serviceStatuses.includes('degraded')) {
      overallStatus = 'degraded'
    } else {
      overallStatus = 'healthy'
    }
    
    const responseTime = Date.now() - startTime
    
    const healthStatus: HealthStatus = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      services,
      metrics: {
        memory: applicationHealth.details?.memory || {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          allocated: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          percentageOfMax: 0,
          percentageOfAllocated: 0
        },
        responseTime
      }
    }
    
    // Return appropriate HTTP status code
    const httpStatus = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 200 : 503
    
    return NextResponse.json(healthStatus, { status: httpStatus })
    
  } catch (error) {
    console.error('Health check failed:', error)
    
    const errorResponse: HealthStatus = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      services: {
        database: { status: 'unhealthy', error: 'Health check failed' },
        redis: { status: 'unhealthy', error: 'Health check failed' },
        application: { status: 'unhealthy', error: 'Health check failed' }
      }
    }
    
    return NextResponse.json(errorResponse, { status: 503 })
  }
}
