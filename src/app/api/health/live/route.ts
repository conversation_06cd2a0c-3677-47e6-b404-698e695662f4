import { NextRequest, NextResponse } from 'next/server'

// Liveness check - indicates if the application is alive and should not be restarted
// This should be very lightweight and only check if the process is responsive
export async function GET(request: NextRequest) {
  try {
    // Very basic checks that don't depend on external services
    const memUsage = process.memoryUsage()
    const uptime = process.uptime()
    
    // Check if memory usage is extremely high (potential memory leak)
    const memoryUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100
    const isMemoryOk = memoryUsagePercent < 95 // Allow up to 95% memory usage
    
    // Check if the process has been running for a reasonable time
    const isUptimeOk = uptime > 0
    
    if (!isMemoryOk || !isUptimeOk) {
      return NextResponse.json({
        status: 'not_alive',
        timestamp: new Date().toISOString(),
        details: {
          memoryUsagePercent: Math.round(memoryUsagePercent),
          uptime,
          issues: [
            ...(!isMemoryOk ? ['high_memory_usage'] : []),
            ...(!isUptimeOk ? ['invalid_uptime'] : [])
          ]
        }
      }, { status: 503 })
    }
    
    return NextResponse.json({
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime,
      memoryUsagePercent: Math.round(memoryUsagePercent)
    })
    
  } catch (error) {
    console.error('Liveness check failed:', error)
    return NextResponse.json({
      status: 'not_alive',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 503 })
  }
}
