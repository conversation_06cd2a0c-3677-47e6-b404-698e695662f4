import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'

// GET /api/benefit-categories - Get all active benefit categories (public endpoint)
export async function GET(request: NextRequest) {
  try {
    const result = await query(`
      SELECT 
        id,
        name,
        display_name,
        description,
        icon,
        sort_order
      FROM benefit_categories
      WHERE is_active = true
      ORDER BY sort_order ASC, display_name ASC
    `)
    
    return NextResponse.json(result.rows)
    
  } catch (error) {
    console.error('Error fetching benefit categories:', error)
    return NextResponse.json(
      { error: 'Failed to fetch benefit categories' },
      { status: 500 }
    )
  }
}
