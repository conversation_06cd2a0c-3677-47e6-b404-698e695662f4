import { NextRequest, NextResponse } from 'next/server'
import { requireAuth, getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { logBenefitRemovalDisputeSubmitted } from '@/lib/activity-logger'

// GET /api/benefit-removal-disputes - Get disputes for a specific company benefit
export async function GET(request: NextRequest) {
  try {
    const userId = await requireAuth()
    const { searchParams } = new URL(request.url)
    const companyBenefitId = searchParams.get('companyBenefitId')

    if (!companyBenefitId) {
      return NextResponse.json(
        { error: 'Company benefit ID is required' },
        { status: 400 }
      )
    }

    // Get disputes for this benefit
    const result = await query(`
      SELECT 
        brd.*,
        u.email as user_email,
        u.first_name as user_first_name,
        u.last_name as user_last_name,
        admin_u.email as admin_email,
        admin_u.first_name as admin_first_name,
        admin_u.last_name as admin_last_name
      FROM benefit_removal_disputes brd
      LEFT JOIN users u ON brd.user_id = u.id
      LEFT JOIN users admin_u ON brd.admin_user_id = admin_u.id
      WHERE brd.company_benefit_id = $1
      ORDER BY brd.created_at DESC
    `, [companyBenefitId])

    return NextResponse.json({
      disputes: result.rows,
      total: result.rows.length,
      approved: result.rows.filter(d => d.status === 'approved').length,
      pending: result.rows.filter(d => d.status === 'pending').length,
      rejected: result.rows.filter(d => d.status === 'rejected').length
    })

  } catch (error) {
    console.error('Error fetching benefit removal disputes:', error)
    return NextResponse.json(
      { error: 'Failed to fetch disputes' },
      { status: 500 }
    )
  }
}

// POST /api/benefit-removal-disputes - Submit a new dispute
export async function POST(request: NextRequest) {
  try {
    const userId = await requireAuth()
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { companyBenefitId, reason } = body

    if (!companyBenefitId || !reason) {
      return NextResponse.json(
        { error: 'Company benefit ID and reason are required' },
        { status: 400 }
      )
    }

    // Check if the benefit exists and is verified
    const benefitResult = await query(`
      SELECT 
        cb.id,
        cb.is_verified,
        c.id as company_id,
        c.name as company_name,
        c.domain as company_domain,
        b.name as benefit_name
      FROM company_benefits cb
      JOIN companies c ON cb.company_id = c.id
      JOIN benefits b ON cb.benefit_id = b.id
      WHERE cb.id = $1
    `, [companyBenefitId])

    if (benefitResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Company benefit not found' },
        { status: 404 }
      )
    }

    const benefit = benefitResult.rows[0]

    // Only allow disputes for verified benefits
    if (!benefit.is_verified) {
      return NextResponse.json(
        { error: 'Only verified benefits can be disputed for removal' },
        { status: 400 }
      )
    }

    // Check domain authorization - user must be from the same company
    const userDomain = user.email.split('@')[1]
    if (userDomain !== benefit.company_domain) {
      return NextResponse.json(
        { error: 'You can only dispute benefits for your own company' },
        { status: 403 }
      )
    }

    // Check if user has already submitted a dispute for this benefit
    const existingDispute = await query(
      'SELECT id FROM benefit_removal_disputes WHERE company_benefit_id = $1 AND user_id = $2',
      [companyBenefitId, userId]
    )

    if (existingDispute.rows.length > 0) {
      return NextResponse.json(
        { error: 'You have already submitted a dispute for this benefit' },
        { status: 400 }
      )
    }

    // Create the dispute
    const disputeResult = await query(`
      INSERT INTO benefit_removal_disputes (company_benefit_id, user_id, reason)
      VALUES ($1, $2, $3)
      RETURNING *
    `, [companyBenefitId, userId, reason])

    const dispute = disputeResult.rows[0]

    // Log the activity
    await logBenefitRemovalDisputeSubmitted(
      benefit.benefit_id,
      benefit.benefit_name,
      benefit.company_id,
      benefit.company_name,
      userId,
      user.email,
      user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : undefined,
      reason
    )

    return NextResponse.json({
      message: 'Dispute submitted successfully',
      dispute: dispute
    })

  } catch (error) {
    console.error('Error submitting benefit removal dispute:', error)
    return NextResponse.json(
      { error: 'Failed to submit dispute' },
      { status: 500 }
    )
  }
}
