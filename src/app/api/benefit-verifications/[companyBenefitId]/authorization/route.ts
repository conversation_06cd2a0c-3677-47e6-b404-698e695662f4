import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ companyBenefitId: string }> }
) {
  try {
    const { companyBenefitId } = await params
    
    // Check if user is authenticated
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({
        authorized: false,
        message: 'You must be signed in to verify benefits',
        requiresAuth: true
      })
    }

    // Check domain authorization
    const authResult = await checkDomainAuthorization(user.email, companyBenefitId)
    
    return NextResponse.json(authResult)
  } catch (error) {
    console.error('Error checking authorization:', error)
    return NextResponse.json(
      { authorized: false, message: 'Authorization check failed' },
      { status: 500 }
    )
  }
}

async function checkDomainAuthorization(userEmail: string, companyBenefitId: string) {
  try {
    // Get company domain from company_benefit with JOIN
    const result = await query(
      `SELECT cb.company_id, c.domain, c.name
       FROM company_benefits cb
       JOIN companies c ON cb.company_id = c.id
       WHERE cb.id = $1`,
      [companyBenefitId]
    )

    if (result.rows.length === 0) {
      return { authorized: false, message: 'Company benefit not found' }
    }

    const company = result.rows[0]
    if (!company) {
      return { authorized: false, message: 'Company not found' }
    }

    if (!company.domain) {
      return {
        authorized: false,
        message: 'Company domain not configured. Contact support to enable benefit verification.',
        companyName: company.name
      }
    }

    // Extract domain from user email
    const userDomain = userEmail.split('@')[1]

    if (userDomain !== company.domain) {
      return {
        authorized: false,
        message: `Employee verification required for ${company.name}`,
        companyName: company.name,
        requiredDomain: company.domain,
        userDomain
      }
    }

    return {
      authorized: true,
      message: 'You are authorized to verify benefits for this company',
      companyName: company.name
    }
  } catch (error) {
    console.error('Error checking domain authorization:', error)
    return { authorized: false, message: 'Authorization check failed' }
  }
}
