'use client'

import { useState, useEffect } from 'react'
import { notFound } from 'next/navigation'
import { MapPin, Users, Building2, CheckCircle, Globe } from 'lucide-react'
import { Header } from '@/components/header'
import { Button } from '@/components/ui/button'

import { SaveCompanyButton } from '@/components/save-company-button'
import { CompanyVerificationNotice } from '@/components/company-verification-notice'
import { BenefitVerificationCounts } from '@/components/benefit-verification-counts'
import { useCompanyAuthorization } from '@/hooks/use-company-authorization'
import type { Company, CompanyBenefit } from '@/types/database'

interface CompanyWithBenefits extends Company {
  company_benefits?: (CompanyBenefit & {
    benefit: {
      id: string
      name: string
      category: string
      icon: string | null
      description: string | null
    }
  })[]
}

interface CompanyPageProps {
  params: Promise<{ id: string }>
}

export default function CompanyPage({ params }: CompanyPageProps) {
  const [company, setCompany] = useState<CompanyWithBenefits | null>(null)
  const [loading, setLoading] = useState(true)
  const [companyId, setCompanyId] = useState<string | null>(null)
  const [mounted, setMounted] = useState(false)

  // Use company-level authorization
  const { authStatus, isLoading: isLoadingAuth } = useCompanyAuthorization(companyId || '')

  useEffect(() => {
    setMounted(true)
    const getParams = async () => {
      const resolvedParams = await params
      setCompanyId(resolvedParams.id)
    }
    getParams()
  }, [params])

  useEffect(() => {
    if (!companyId) return

    const fetchCompany = async () => {
      try {
        const response = await fetch(`/api/companies/${companyId}`)
        if (response.ok) {
          const data = await response.json()
          setCompany(data)

          // Track company page view
          try {
            await fetch('/api/analytics/track', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                type: 'company_view',
                data: {
                  companyId: companyId,
                  referrer: document.referrer || undefined
                }
              })
            })
          } catch (trackingError) {
            // Don't break the page if tracking fails
            console.warn('Failed to track company view:', trackingError)
          }
        } else {
          notFound()
        }
      } catch (error) {
        console.error('Error fetching company:', error)
        notFound()
      } finally {
        setLoading(false)
      }
    }

    fetchCompany()
  }, [companyId])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-6xl mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!company) {
    notFound()
  }

  const benefits = company.company_benefits || []
  const verifiedBenefits = benefits.filter(cb => cb.is_verified)
  const unverifiedBenefits = benefits.filter(cb => !cb.is_verified)

  const getSizeLabel = (size: string) => {
    const labels = {
      startup: 'Startup',
      small: 'Small (1-50 employees)',
      medium: 'Medium (51-200 employees)',
      large: 'Large (201-1000 employees)',
      enterprise: 'Enterprise (1000+ employees)'
    }
    return labels[size as keyof typeof labels] || size
  }

  const getBenefitsByCategory = (benefits: typeof company.company_benefits) => {
    const categories: Record<string, typeof benefits> = {}
    benefits?.forEach(cb => {
      const category = cb.benefit?.category || 'other'
      if (!categories[category]) categories[category] = []
      categories[category].push(cb)
    })
    return categories
  }

  const categorizedBenefits = getBenefitsByCategory(benefits)

  return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="container mx-auto px-4 py-8">
          {/* Company Header */}
          <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6 lg:p-8 mb-8">
            <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-6 space-y-4 lg:space-y-0">
              <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                <div className="w-12 h-12 sm:w-16 sm:h-16 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Building2 className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">
                    <span className="break-words">{company.name}</span>
                  </h1>
                  <div className="mt-2">
                    <span className="bg-gray-100 px-2 py-1 sm:px-3 rounded-full text-xs sm:text-sm inline-block w-fit text-gray-600 mb-3">
                      {company.industry}
                    </span>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:items-center text-gray-600 space-y-2 sm:space-y-0 sm:space-x-4 lg:space-x-6">
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 sm:w-5 sm:h-5 mr-2 flex-shrink-0" />
                      <span className="text-sm sm:text-base">{company.location}</span>
                    </div>
                    <div className="flex items-center">
                      <Users className="w-4 h-4 sm:w-5 sm:h-5 mr-2 flex-shrink-0" />
                      <span className="text-sm sm:text-base">{getSizeLabel(company.size)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {company.domain && (
                <div className="flex items-center text-gray-600 lg:flex-shrink-0">
                  <Globe className="w-4 h-4 mr-2 flex-shrink-0" />
                  <a
                    href={`https://${company.domain}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:text-blue-600 text-sm sm:text-base break-all"
                  >
                    {company.domain}
                  </a>
                </div>
              )}
            </div>

            {company.description && (
              <div className="mb-6">
                <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-2">About</h2>
                <p className="text-sm sm:text-base text-gray-600 leading-relaxed">{company.description}</p>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <Button
                onClick={() => {
                  if (!mounted) return
                  if (company.career_url) {
                    window.open(company.career_url, '_blank', 'noopener,noreferrer')
                  } else {
                    alert('No career page available for this company')
                  }
                }}
                className="flex items-center justify-center gap-2 w-full sm:w-auto"
              >
                Apply Now
                {company.career_url && <span>↗</span>}
              </Button>
              <SaveCompanyButton companyId={company.id} companyName={company.name} />
            </div>
          </div>

          {/* Benefits Section */}
          <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6 lg:p-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-2 sm:space-y-0">
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900">Employee Benefits</h2>
              <div className="text-xs sm:text-sm text-gray-600">
                {verifiedBenefits.length} verified • {unverifiedBenefits.length} pending verification
              </div>
            </div>

            {Object.keys(categorizedBenefits).length > 0 ? (
              <div className="space-y-8">
                {Object.entries(categorizedBenefits).map(([category, categoryBenefits]) => (
                  <div key={category}>
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-4 capitalize">
                      {category.replace('_', ' ')} Benefits
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                      {categoryBenefits?.map((companyBenefit) => (
                        <div key={companyBenefit.id} className="space-y-3">
                          <div
                            className={`p-3 sm:p-4 rounded-lg border ${
                              companyBenefit.is_verified
                                ? 'bg-green-50 border-green-200'
                                : 'bg-gray-50 border-gray-200'
                            }`}
                          >
                            <div className="flex items-start justify-between gap-2">
                              <div className="flex items-center space-x-2 min-w-0 flex-1">
                                {companyBenefit.benefit?.icon && (
                                  <span className="text-base sm:text-lg flex-shrink-0">{companyBenefit.benefit.icon}</span>
                                )}
                                <span className="font-medium text-gray-900 text-sm sm:text-base break-words">
                                  {companyBenefit.benefit?.name}
                                </span>
                              </div>
                              {companyBenefit.is_verified ? (
                                <div className="flex items-center space-x-1 flex-shrink-0">
                                  <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-600" />
                                  <span className="text-xs text-green-700 font-medium hidden sm:inline">Verified</span>
                                </div>
                              ) : (
                                <div className="flex items-center space-x-1 flex-shrink-0">
                                  <div className="w-3 h-3 sm:w-4 sm:h-4 rounded-full bg-yellow-400 flex items-center justify-center">
                                    <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-white"></div>
                                  </div>
                                  <span className="text-xs text-yellow-700 font-medium hidden sm:inline">Pending</span>
                                </div>
                              )}
                            </div>

                            {/* Verification counts for all users */}
                            <BenefitVerificationCounts companyBenefitId={companyBenefit.id} />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600">No benefits information available for this company.</p>
              </div>
            )}

            {/* Company-level verification notice */}
            {!isLoadingAuth && authStatus && !authStatus.authorized && (
              <div className="mt-8">
                <CompanyVerificationNotice authStatus={authStatus} />
              </div>
            )}
          </div>
        </main>
      </div>
    )
}
