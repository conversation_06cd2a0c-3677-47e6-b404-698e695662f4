import { redirect } from 'next/navigation'
import { Header } from '@/components/header'
import { AdminDashboard } from '@/components/admin-dashboard'
import { getCurrentUser } from '@/lib/auth'

export default async function AdminPage() {
  const user = await getCurrentUser()

  if (!user) {
    redirect('/sign-in')
  }

  // Check if user has admin role
  if (user.role !== 'admin') {
    redirect('/')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Platform Administration
          </h1>
          <p className="text-gray-600">
            Manage companies, users, benefits, and platform-wide settings
          </p>
        </div>

        <AdminDashboard />
      </main>
    </div>
  )
}
