'use client'

import { useState, useEffect } from 'react'
import {
  TrendingUp,
  TrendingDown,
  Award,
  Star,
  BarChart3,
  Users,
  Crown,
  Info,
  Calendar,
  Filter,
  Download
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface BenefitRankingAnalytics {
  period: string
  category: string
  summary: {
    totalRankings: number
    totalBenefitsRanked: number
    averageOverallRanking: number
    mostImportantBenefit: string | null
    leastImportantBenefit: string | null
  }
  benefitStats: Array<{
    benefit_id: string
    benefit_name: string
    category: string
    icon: string | null
    category_display_name: string
    total_rankings: number
    average_ranking: number
    best_ranking: number
    worst_ranking: number
    top_3_count: number
    bottom_3_count: number
    ranking_stddev: number | null
    importance_score: number
  }>
  categoryBreakdown: Array<{
    category: string
    category_display_name: string
    total_rankings: number
    average_ranking: number
    top_3_count: number
  }>
  improvements: Array<{
    benefit_name: string
    category: string
    icon: string | null
    current_avg: number
    previous_avg: number
    improvement: number
  }>
  is_demo_data: boolean
  demo_notice?: string
}

interface BenefitRankingAnalyticsProps {
  className?: string
  period?: string
  category?: string
}

export function BenefitRankingAnalytics({ className, period = '30d', category = 'all' }: BenefitRankingAnalyticsProps) {
  const [data, setData] = useState<BenefitRankingAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [userPaymentStatus, setUserPaymentStatus] = useState<string>('')

  useEffect(() => {
    fetchAnalytics()
    fetchUserStatus()
  }, [period, category])

  const fetchUserStatus = async () => {
    try {
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        const data = await response.json()
        setUserPaymentStatus(data.user?.paymentStatus || 'free')
      }
    } catch (error) {
      console.error('Error fetching user status:', error)
    }
  }

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        period,
        limit: '20'
      })
      
      if (category !== 'all') {
        params.append('category', category)
      }

      const response = await fetch(`/api/analytics/benefit-rankings?${params}`)
      
      if (response.ok) {
        const analyticsData = await response.json()
        setData(analyticsData)
      } else {
        const errorData = await response.text()
        console.error('Failed to fetch ranking analytics:', response.status, response.statusText, errorData)
      }
    } catch (error) {
      console.error('Error fetching ranking analytics:', error)
      // Set empty data structure to prevent infinite loading
      setData({
        period,
        category: category || 'all',
        summary: {
          totalRankings: 0,
          totalBenefitsRanked: 0,
          averageOverallRanking: 0,
          mostImportantBenefit: null,
          leastImportantBenefit: null
        },
        benefitStats: [],
        categoryBreakdown: [],
        improvements: [],
        is_demo_data: false
      })
    } finally {
      setLoading(false)
    }
  }

  const getRankingColor = (ranking: number) => {
    if (ranking <= 3) return 'text-green-600 bg-green-50 border-green-200'
    if (ranking <= 6) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  const getImportanceColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-50'
    if (score >= 60) return 'text-yellow-600 bg-yellow-50'
    return 'text-red-600 bg-red-50'
  }

  const getImprovementIcon = (improvement: number) => {
    if (improvement > 0) return <TrendingUp className="w-4 h-4 text-green-600" />
    if (improvement < 0) return <TrendingDown className="w-4 h-4 text-red-600" />
    return <BarChart3 className="w-4 h-4 text-gray-500" />
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading ranking analytics...</p>
        </div>
      </div>
    )
  }

  if (!data || (data.summary.totalRankings === 0 && !data.is_demo_data)) {
    return (
      <div className="text-center py-8">
        <BarChart3 className="w-12 h-12 mx-auto mb-4 text-gray-500" />
        <p className="text-gray-600">No ranking data available yet</p>
        <p className="text-sm text-gray-500 mt-2">
          Start ranking benefits to see analytics here
        </p>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Benefit Rankings</h3>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            disabled={data?.is_demo_data}
            title={data?.is_demo_data ? 'Premium feature - Upgrade to export data' : 'Export as CSV'}
          >
            <Download className="w-4 h-4" />
            Export CSV
            {data?.is_demo_data && <Crown className="w-3 h-3 ml-1 text-amber-500" />}
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            disabled={data?.is_demo_data}
            title={data?.is_demo_data ? 'Premium feature - Upgrade to export data' : 'Export as JSON'}
          >
            <Download className="w-4 h-4" />
            Export JSON
            {data?.is_demo_data && <Crown className="w-3 h-3 ml-1 text-amber-500" />}
          </Button>
        </div>
      </div>

      {/* Desktop Table */}
      <div className="hidden md:block overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="text-left py-3 px-4 font-medium text-gray-900">Rank</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Benefit</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Category</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Avg Rating</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Total Ratings</th>
              <th className="text-left py-3 px-4 font-medium text-gray-900">Importance</th>
            </tr>
          </thead>
          <tbody>
            {data?.benefitStats.slice(0, 20).map((benefit, index) => (
              <tr key={benefit.benefit_id} className="border-b border-gray-100 hover:bg-gray-50">
                <td className="py-3 px-4">
                  <span className="inline-flex items-center justify-center w-6 h-6 bg-gray-100 text-gray-700 text-sm font-medium rounded">
                    {index + 1}
                  </span>
                </td>
                <td className="py-3 px-4">
                  <div className="flex items-center space-x-2">
                    {benefit.icon && <span className="text-lg">{benefit.icon}</span>}
                    <span className="font-medium text-gray-900">{benefit.benefit_name}</span>
                  </div>
                </td>
                <td className="py-3 px-4">
                  <span className="text-gray-600 capitalize">{benefit.category_display_name}</span>
                </td>
                <td className="py-3 px-4">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRankingColor(benefit.average_ranking)}`}>
                    {benefit.average_ranking}
                  </span>
                </td>
                <td className="py-3 px-4">
                  <span className="text-gray-900 font-medium">{benefit.total_rankings.toLocaleString()}</span>
                </td>
                <td className="py-3 px-4">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getImportanceColor(benefit.importance_score)}`}>
                    {benefit.importance_score}%
                  </span>
                </td>
              </tr>
            )) || []}
          </tbody>
        </table>
      </div>

      {/* Mobile Cards */}
      <div className="md:hidden space-y-4">
        {data?.benefitStats.slice(0, 20).map((benefit, index) => (
          <div key={benefit.benefit_id} className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <span className="inline-flex items-center justify-center w-6 h-6 bg-gray-100 text-gray-700 text-sm font-medium rounded">
                  {index + 1}
                </span>
                {benefit.icon && <span className="text-lg">{benefit.icon}</span>}
                <span className="font-medium text-gray-900 truncate">{benefit.benefit_name}</span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Category:</span>
                <span className="ml-1 text-gray-900 capitalize">{benefit.category_display_name}</span>
              </div>
              <div>
                <span className="text-gray-500">Avg Rating:</span>
                <span className={`ml-1 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRankingColor(benefit.average_ranking)}`}>
                  {benefit.average_ranking}
                </span>
              </div>
              <div>
                <span className="text-gray-500">Total Ratings:</span>
                <span className="ml-1 text-gray-900 font-medium">{benefit.total_rankings.toLocaleString()}</span>
              </div>
              <div>
                <span className="text-gray-500">Importance:</span>
                <span className={`ml-1 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getImportanceColor(benefit.importance_score)}`}>
                  {benefit.importance_score}%
                </span>
              </div>
            </div>
          </div>
        )) || []}
      </div>
    </div>
  )
}
