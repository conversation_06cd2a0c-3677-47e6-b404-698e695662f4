'use client'

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { CheckCircle, XCircle, Loader2, Mail } from 'lucide-react'
import Link from 'next/link'

interface VerificationResult {
  success: boolean
  message?: string
  user?: {
    id: string
    email: string
    firstName: string | null
    lastName: string | null
    role: string
  }
}

export function MagicLinkVerification() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const token = searchParams.get('token')

  const [isVerifying, setIsVerifying] = useState(!!token) // Start as true if token exists
  const [result, setResult] = useState<VerificationResult | null>(null)

  useEffect(() => {
    if (token) {
      verifyMagicLink(token)
    } else {
      setIsVerifying(false) // Set to false when no token
      setResult({
        success: false,
        message: 'No verification token provided'
      })
    }
  }, [token])

  const verifyMagicLink = async (verificationToken: string) => {
    setIsVerifying(true)
    try {
      const response = await fetch('/api/auth/magic-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: verificationToken }),
      })

      const data = await response.json()
      
      if (response.ok) {
        setResult({
          success: true,
          user: data.user
        })
        
        // Redirect to dashboard after a short delay
        setTimeout(() => {
          router.push('/dashboard')
          router.refresh()
        }, 2000)
      } else {
        setResult({
          success: false,
          message: data.error || 'Failed to verify magic link'
        })
      }
    } catch (error) {
      console.error('Error verifying magic link:', error)
      setResult({
        success: false,
        message: 'An error occurred while verifying your magic link'
      })
    } finally {
      setIsVerifying(false)
    }
  }

  return (
    <div className="bg-white shadow-lg rounded-lg p-8">
      {isVerifying ? (
        <div className="text-center">
          <Loader2 className="w-12 h-12 text-blue-600 animate-spin mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Verifying...
          </h2>
          <p className="text-gray-600">
            Please wait while we verify your magic link.
          </p>
        </div>
      ) : result ? (
        <div className="text-center">
          {result.success ? (
            <>
              <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Authentication Successful!
              </h2>
              <p className="text-gray-600 mb-6">
                Welcome to WorkWell! You&apos;re being redirected to your dashboard...
              </p>
              {result.user && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                  <div className="flex items-center justify-center space-x-2">
                    <Mail className="w-5 h-5 text-green-600" />
                    <span className="text-sm text-green-800">
                      Signed in as <strong>{result.user.email}</strong>
                    </span>
                  </div>
                </div>
              )}
              <div className="space-y-3">
                <Link
                  href="/dashboard"
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors inline-block text-center"
                >
                  Go to Dashboard
                </Link>
                <Link
                  href="/"
                  className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors inline-block text-center"
                >
                  Back to Home
                </Link>
              </div>
            </>
          ) : (
            <>
              <XCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Verification Failed
              </h2>
              <p className="text-gray-600 mb-6">
                {result.message || 'The magic link is invalid or has expired.'}
              </p>
              <div className="space-y-3">
                <Link
                  href="/sign-in"
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors inline-block text-center"
                >
                  Try Signing In Again
                </Link>
                <Link
                  href="/"
                  className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors inline-block text-center"
                >
                  Back to Home
                </Link>
              </div>
            </>
          )}
        </div>
      ) : (
        <div className="text-center">
          <XCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Invalid Link
          </h2>
          <p className="text-gray-600 mb-6">
            This magic link is invalid or missing required parameters.
          </p>
          <Link
            href="/sign-in"
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors inline-block text-center"
          >
            Sign In
          </Link>
        </div>
      )}
    </div>
  )
}
