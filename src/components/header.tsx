'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'
import { Menu, X } from 'lucide-react'
import { UserButton } from '@/components/local-auth/user-button'
import { Button } from '@/components/ui/button'

export function Header() {
  const [isSignedIn, setIsSignedIn] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isAdmin, setIsAdmin] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  useEffect(() => {
    setMounted(true)
    checkAuthStatus()
  }, [])

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (isMobileMenuOpen && !target.closest('header')) {
        setIsMobileMenuOpen(false)
      }
    }

    if (isMobileMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isMobileMenuOpen])

  const checkAuthStatus = async () => {
    try {
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        const data = await response.json()
        setIsSignedIn(true)
        setIsAdmin(data.user?.role === 'admin')
      } else {
        setIsSignedIn(false)
        setIsAdmin(false)
      }
    } catch {
      setIsSignedIn(false)
      setIsAdmin(false)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <header className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4 py-3 sm:py-4 flex items-center justify-between">
        <Link href="/" className="flex items-center space-x-2 flex-shrink-0">
          <div className="w-7 h-7 sm:w-8 sm:h-8 bg-green-600 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-base sm:text-lg">✓</span>
          </div>
          <span className="text-lg sm:text-xl font-bold text-gray-900">Workwell</span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden lg:flex items-center space-x-4 xl:space-x-6">
          <Link href="/" className="text-gray-700 hover:text-gray-900 text-sm xl:text-base">
            Companies
          </Link>
          <Link href="/benefits" className="text-gray-700 hover:text-gray-900 text-sm xl:text-base">
            Benefits
          </Link>
          {mounted && isSignedIn && (
            <>
              <Link href="/saved-companies" className="text-gray-700 hover:text-gray-900 text-sm xl:text-base">
                Saved Companies
              </Link>
              <Link href="/rankings" className="text-gray-700 hover:text-gray-900 text-sm xl:text-base">
                Rankings
              </Link>
            </>
          )}
          <Link href="/about" className="text-gray-700 hover:text-gray-900 text-sm xl:text-base">
            About
          </Link>
          {mounted && isSignedIn && (
            <>
              <Link href="/analytics" className="text-gray-700 hover:text-gray-900 text-sm xl:text-base">
                Analytics
              </Link>
              {isAdmin && (
                <Link href="/admin" className="text-gray-700 hover:text-gray-900 text-sm xl:text-base">
                  Admin
                </Link>
              )}
            </>
          )}
        </nav>

        <div className="flex items-center space-x-2 sm:space-x-3 lg:space-x-4">
          {/* Mobile menu button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="lg:hidden p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Toggle mobile menu"
          >
            {isMobileMenuOpen ? (
              <X className="w-5 h-5" />
            ) : (
              <Menu className="w-5 h-5" />
            )}
          </button>

          {!mounted || isLoading ? (
            <div className="w-16 sm:w-20 h-8 bg-gray-200 animate-pulse rounded"></div>
          ) : isSignedIn ? (
            <>
              <Link href="/dashboard" className="hidden sm:block">
                <Button variant="outline" size="sm" className="text-xs sm:text-sm">
                  My Company
                </Button>
              </Link>
              <UserButton />
            </>
          ) : (
            <>
              <Link href="/sign-in">
                <Button variant="outline" size="sm" className="text-xs sm:text-sm px-2 sm:px-3">
                  Sign In
                </Button>
              </Link>
              <Link href="/sign-up">
                <Button size="sm" className="text-xs sm:text-sm px-2 sm:px-3">
                  <span className="hidden sm:inline">Join your company</span>
                  <span className="sm:hidden">Join</span>
                </Button>
              </Link>
            </>
          )}
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div className="lg:hidden bg-white border-t border-gray-200">
          <div className="container mx-auto px-4 py-4">
            <nav className="flex flex-col space-y-4">
              <Link
                href="/"
                className="text-gray-700 hover:text-gray-900 py-2 text-base font-medium"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Companies
              </Link>
              <Link
                href="/benefits"
                className="text-gray-700 hover:text-gray-900 py-2 text-base font-medium"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Benefits
              </Link>
              {mounted && isSignedIn && (
                <>
                  <Link
                    href="/saved-companies"
                    className="text-gray-700 hover:text-gray-900 py-2 text-base font-medium"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Saved Companies
                  </Link>
                  <Link
                    href="/rankings"
                    className="text-gray-700 hover:text-gray-900 py-2 text-base font-medium"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Rankings
                  </Link>
                  <Link
                    href="/dashboard"
                    className="text-gray-700 hover:text-gray-900 py-2 text-base font-medium sm:hidden"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    My Company
                  </Link>
                </>
              )}
              <Link
                href="/about"
                className="text-gray-700 hover:text-gray-900 py-2 text-base font-medium"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                About
              </Link>
              {mounted && isSignedIn && (
                <>
                  <Link
                    href="/analytics"
                    className="text-gray-700 hover:text-gray-900 py-2 text-base font-medium"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Analytics
                  </Link>
                  {isAdmin && (
                    <Link
                      href="/admin"
                      className="text-gray-700 hover:text-gray-900 py-2 text-base font-medium"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Admin
                    </Link>
                  )}
                </>
              )}
            </nav>
          </div>
        </div>
      )}
    </header>
  )
}
