'use client'

import { useState, useEffect } from 'react'
import { Heart, HeartOff } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface SaveCompanyButtonProps {
  companyId: string
  companyName: string
}

export function SaveCompanyButton({ companyId, companyName }: SaveCompanyButtonProps) {
  const [isSaved, setIsSaved] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Check if company is already saved on component mount
  useEffect(() => {
    const checkSavedStatus = async () => {
      try {
        const response = await fetch(`/api/saved-companies/${companyId}`)
        if (response.ok) {
          const data = await response.json()
          setIsSaved(data.saved)
        }
      } catch (error) {
        console.error('Error checking saved status:', error)
      }
    }

    checkSavedStatus()
  }, [companyId])

  const handleSaveToggle = async () => {
    setIsLoading(true)

    try {
      if (isSaved) {
        // Remove from saved companies
        const response = await fetch(`/api/saved-companies?companyId=${companyId}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          setIsSaved(false)
        } else {
          const error = await response.json()
          alert(error.error || 'Failed to remove company from saved list')
        }
      } else {
        // Add to saved companies
        const response = await fetch('/api/saved-companies', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ companyId })
        })

        if (response.ok) {
          setIsSaved(true)
        } else {
          const error = await response.json()
          if (response.status === 401) {
            alert('Please sign in to save companies')
          } else {
            alert(error.error || 'Failed to save company')
          }
        }
      }
    } catch (error) {
      console.error('Error saving company:', error)
      alert('Failed to save company')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button
      variant="outline"
      onClick={handleSaveToggle}
      disabled={isLoading}
      className={`flex items-center justify-center gap-2 w-full sm:w-auto ${isSaved ? 'text-red-600 border-red-300 hover:bg-red-50' : ''}`}
    >
      {isSaved ? (
        <>
          <Heart className="w-4 h-4 fill-current" />
          <span className="text-sm sm:text-base">Saved</span>
        </>
      ) : (
        <>
          <HeartOff className="w-4 h-4" />
          <span className="text-sm sm:text-base">Save Company</span>
        </>
      )}
    </Button>
  )
}
