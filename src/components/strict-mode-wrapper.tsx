'use client'

import { ReactNode } from 'react'

interface StrictModeWrapperProps {
  children: ReactNode
  disabled?: boolean
}

/**
 * Wrapper component that conditionally disables React Strict Mode
 * for components that have issues with double-invocation in development.
 * 
 * Use sparingly and only when necessary to fix duplicate API calls
 * or other side effects that are problematic in development.
 */
export function StrictModeWrapper({ children, disabled = false }: StrictModeWrapperProps) {
  // In development, if disabled is true, we wrap in a fragment to avoid Strict Mode
  // In production, Strict Mode is not active anyway, so this has no effect
  if (disabled && process.env.NODE_ENV === 'development') {
    return <>{children}</>
  }
  
  return <>{children}</>
}
