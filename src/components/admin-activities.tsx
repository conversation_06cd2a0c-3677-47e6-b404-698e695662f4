'use client'

import { useState, useEffect } from 'react'
import { Search, Filter, Calendar, Download, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { SafeDate } from '@/components/ui/safe-date'
import { Pagination } from '@/components/ui/pagination'

interface Activity {
  id: string
  event_type: string
  event_description: string
  user_id?: string
  user_email?: string
  user_name?: string
  company_id?: string
  company_name?: string
  benefit_id?: string
  benefit_name?: string
  metadata?: Record<string, any>
  created_at: string
}

interface ActivitiesResponse {
  activities: Activity[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

const EVENT_TYPE_LABELS: Record<string, string> = {
  'company_added': 'Company Added',
  'user_registered': 'User Registered',
  'benefit_verified': 'Benefit Verified',
  'benefit_disputed': 'Benefit Disputed',
  'benefit_removal_dispute_submitted': 'Dispute Submitted',
  'benefit_removal_dispute_approved': 'Dispute Approved',
  'benefit_removal_dispute_rejected': 'Dispute Rejected',
  'benefit_removal_dispute_cancelled': 'Dispute Cancelled',
  'benefit_automatically_removed': 'Benefit Auto-Removed'
}

const EVENT_TYPE_COLORS: Record<string, string> = {
  'company_added': 'bg-blue-100 text-blue-800',
  'user_registered': 'bg-green-100 text-green-800',
  'benefit_verified': 'bg-emerald-100 text-emerald-800',
  'benefit_disputed': 'bg-yellow-100 text-yellow-800',
  'benefit_removal_dispute_submitted': 'bg-orange-100 text-orange-800',
  'benefit_removal_dispute_approved': 'bg-red-100 text-red-800',
  'benefit_removal_dispute_rejected': 'bg-gray-100 text-gray-800',
  'benefit_removal_dispute_cancelled': 'bg-purple-100 text-purple-800',
  'benefit_automatically_removed': 'bg-red-100 text-red-800'
}

export function AdminActivities() {
  const [activities, setActivities] = useState<Activity[]>([])
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0
  })

  // Filters
  const [search, setSearch] = useState('')
  const [eventType, setEventType] = useState('all')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')

  const fetchActivities = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      })

      if (search) params.append('search', search)
      if (eventType !== 'all') params.append('eventType', eventType)
      if (startDate) params.append('startDate', startDate)
      if (endDate) params.append('endDate', endDate)

      const response = await fetch(`/api/admin/activities/all?${params}`)
      if (!response.ok) throw new Error('Failed to fetch activities')

      const data: ActivitiesResponse = await response.json()
      setActivities(data.activities)
      setPagination(data.pagination)
    } catch (error) {
      console.error('Error fetching activities:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchActivities()
  }, [pagination.page, pagination.limit])

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }))
    fetchActivities()
  }

  const handleReset = () => {
    setSearch('')
    setEventType('all')
    setStartDate('')
    setEndDate('')
    setPagination(prev => ({ ...prev, page: 1 }))
    setTimeout(fetchActivities, 0)
  }

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }))
  }

  const getActivityColor = (eventType: string) => {
    return EVENT_TYPE_COLORS[eventType] || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">All Activities</h3>
          <p className="text-sm text-gray-600 mt-1">Complete log of all system activities</p>
        </div>
        <Button
          onClick={fetchActivities}
          disabled={loading}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          {/* Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-4 h-4" />
              <input
                type="text"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                placeholder="User, company, benefit..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
          </div>

          {/* Event Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Event Type
            </label>
            <select
              value={eventType}
              onChange={(e) => setEventType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Events</option>
              {Object.entries(EVENT_TYPE_LABELS).map(([value, label]) => (
                <option key={value} value={value}>{label}</option>
              ))}
            </select>
          </div>

          {/* Start Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Start Date
            </label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* End Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              End Date
            </label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Filter Actions */}
        <div className="flex items-center gap-3">
          <Button onClick={handleSearch} className="flex items-center gap-2">
            <Filter className="w-4 h-4" />
            Apply Filters
          </Button>
          <Button variant="outline" onClick={handleReset}>
            Reset
          </Button>
        </div>
      </div>

      {/* Activities List */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-md font-semibold text-gray-900">
            Activities ({pagination.total})
          </h4>
        </div>

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading activities...</span>
            </div>
          ) : activities.length > 0 ? (
            <div className="space-y-4">
              {activities.map((activity) => (
                <div key={activity.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getActivityColor(activity.event_type)}`}>
                          {EVENT_TYPE_LABELS[activity.event_type] || activity.event_type}
                        </span>
                        <SafeDate date={activity.created_at} format="datetime" className="text-sm text-gray-500" />
                      </div>
                      <p className="text-gray-900 mb-2">{activity.event_description}</p>
                      
                      {/* Additional Details */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        {activity.user_email && (
                          <div>
                            <span className="font-medium">User:</span> {activity.user_name || activity.user_email}
                          </div>
                        )}
                        {activity.company_name && (
                          <div>
                            <span className="font-medium">Company:</span> {activity.company_name}
                          </div>
                        )}
                        {activity.benefit_name && (
                          <div>
                            <span className="font-medium">Benefit:</span> {activity.benefit_name}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">No activities found matching your criteria.</p>
            </div>
          )}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <Pagination
          currentPage={pagination.page}
          totalPages={pagination.totalPages}
          totalItems={pagination.total}
          itemsPerPage={pagination.limit}
          itemName="activities"
          onPageChange={handlePageChange}
        />
      )}
    </div>
  )
}
