'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { CheckCircle, XCircle, Loader2, Mail } from 'lucide-react'
import Link from 'next/link'

interface VerificationResult {
  success: boolean
  message: string
  companyName?: string
  userEmail?: string
}

export function CompanyVerification() {
  const searchParams = useSearchParams()
  const token = searchParams.get('token')
  
  const [isVerifying, setIsVerifying] = useState(false)
  const [result, setResult] = useState<VerificationResult | null>(null)

  useEffect(() => {
    if (token) {
      verifyToken(token)
    } else {
      setResult({
        success: false,
        message: 'No verification token provided'
      })
    }
  }, [token])

  const verifyToken = async (verificationToken: string) => {
    setIsVerifying(true)
    try {
      const response = await fetch('/api/company-verification/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: verificationToken }),
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      console.error('Error verifying token:', error)
      setResult({
        success: false,
        message: 'An error occurred while verifying your company association'
      })
    } finally {
      setIsVerifying(false)
    }
  }

  return (
    <div className="bg-white shadow-lg rounded-lg p-8">
      {isVerifying ? (
        <div className="text-center">
          <Loader2 className="w-12 h-12 text-blue-600 animate-spin mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Verifying...
          </h2>
          <p className="text-gray-600">
            Please wait while we verify your company association.
          </p>
        </div>
      ) : result ? (
        <div className="text-center">
          {result.success ? (
            <>
              <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Verification Successful!
              </h2>
              <p className="text-gray-600 mb-6">
                {result.message}
              </p>
              {result.companyName && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                  <div className="flex items-center justify-center space-x-2">
                    <Mail className="w-5 h-5 text-green-600" />
                    <span className="text-sm text-green-800">
                      <strong>{result.userEmail}</strong> is now verified for <strong>{result.companyName}</strong>
                    </span>
                  </div>
                </div>
              )}
              <div className="space-y-3">
                <Link
                  href="/dashboard"
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors inline-block text-center"
                >
                  Go to Dashboard
                </Link>
                <Link
                  href="/"
                  className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors inline-block text-center"
                >
                  Back to Home
                </Link>
              </div>
            </>
          ) : (
            <>
              <XCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Verification Failed
              </h2>
              <p className="text-gray-600 mb-6">
                {result.message}
              </p>
              <div className="space-y-3">
                <Link
                  href="/"
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors inline-block text-center"
                >
                  Back to Home
                </Link>
                <Link
                  href="/sign-in"
                  className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors inline-block text-center"
                >
                  Sign In
                </Link>
              </div>
            </>
          )}
        </div>
      ) : (
        <div className="text-center">
          <XCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Invalid Link
          </h2>
          <p className="text-gray-600 mb-6">
            This verification link is invalid or missing required parameters.
          </p>
          <Link
            href="/"
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors inline-block text-center"
          >
            Back to Home
          </Link>
        </div>
      )}
    </div>
  )
}
