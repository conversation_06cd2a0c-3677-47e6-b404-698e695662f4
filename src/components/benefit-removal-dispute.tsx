'use client'

import { useState, useEffect } from 'react'
import { AlertTriangle, Flag, CheckCircle, XCircle, Clock } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface DisputeStats {
  pending: number
  approved: number
  rejected: number
  total: number
}

interface UserDispute {
  id: string
  reason: string
  status: 'pending' | 'approved' | 'rejected' | 'cancelled'
  created_at: string
  admin_comment?: string
}

interface BenefitRemovalDisputeProps {
  companyBenefitId: string
  benefitName: string
  companyName: string
  onDisputeSubmitted?: () => void
}

export function BenefitRemovalDispute({
  companyBenefitId,
  benefitName,
  companyName,
  onDisputeSubmitted
}: BenefitRemovalDisputeProps) {
  const [disputeStats, setDisputeStats] = useState<DisputeStats>({ pending: 0, approved: 0, rejected: 0, total: 0 })
  const [userDispute, setUserDispute] = useState<UserDispute | null>(null)
  const [canDispute, setCanDispute] = useState(false)
  const [showDisputeForm, setShowDisputeForm] = useState(false)
  const [reason, setReason] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isCancelling, setIsCancelling] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDisputeStatus()
  }, [companyBenefitId])

  const fetchDisputeStatus = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/benefit-removal-disputes/${companyBenefitId}`)
      if (response.ok) {
        const data = await response.json()
        setDisputeStats(data.stats)
        setUserDispute(data.userDispute)
        setCanDispute(data.canDispute)
      }
    } catch (error) {
      console.error('Error fetching dispute status:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmitDispute = async () => {
    if (!reason.trim()) {
      alert('Please provide a reason for the dispute')
      return
    }

    setIsSubmitting(true)
    try {
      const response = await fetch('/api/benefit-removal-disputes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          companyBenefitId,
          reason: reason.trim()
        })
      })

      if (response.ok) {
        alert('Dispute submitted successfully!')
        setShowDisputeForm(false)
        setReason('')
        fetchDisputeStatus()
        onDisputeSubmitted?.()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to submit dispute')
      }
    } catch (error) {
      console.error('Error submitting dispute:', error)
      alert('Failed to submit dispute')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancelDispute = async () => {
    if (!userDispute) return

    const confirmed = confirm('Are you sure you want to cancel your dispute? This action cannot be undone.')
    if (!confirmed) return

    setIsCancelling(true)
    try {
      const response = await fetch('/api/benefit-removal-disputes/cancel', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          disputeId: userDispute.id
        })
      })

      if (response.ok) {
        alert('Dispute cancelled successfully!')
        fetchDisputeStatus()
        onDisputeSubmitted?.()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to cancel dispute')
      }
    } catch (error) {
      console.error('Error cancelling dispute:', error)
      alert('Failed to cancel dispute')
    } finally {
      setIsCancelling(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center gap-2 text-sm text-gray-500">
        <div className="animate-spin rounded-full h-3 w-3 border-b border-gray-400"></div>
        Loading dispute status...
      </div>
    )
  }

  const requiresApprovals = 2
  const progressPercentage = Math.min((disputeStats.approved / requiresApprovals) * 100, 100)

  // Only show dispute sections if there are active (non-cancelled) disputes
  const hasActiveDisputes = disputeStats.pending > 0 || disputeStats.approved > 0 || disputeStats.rejected > 0
  const hasActiveUserDispute = userDispute && userDispute.status !== 'cancelled'

  return (
    <div className="space-y-3">
      {/* Dispute Status Display */}
      {hasActiveDisputes && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <div className="flex items-center gap-2 mb-2">
            <AlertTriangle className="w-4 h-4 text-yellow-600" />
            <span className="text-sm font-medium text-yellow-800">
              Removal Disputes: {disputeStats.approved}/{requiresApprovals} approved
            </span>
          </div>
          
          {/* Progress bar */}
          <div className="w-full bg-yellow-200 rounded-full h-2 mb-2">
            <div 
              className="bg-yellow-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
          
          <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-xs text-yellow-700">
            <span className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              {disputeStats.pending} pending
            </span>
            <span className="flex items-center gap-1">
              <CheckCircle className="w-3 h-3" />
              {disputeStats.approved} approved
            </span>
            <span className="flex items-center gap-1">
              <XCircle className="w-3 h-3" />
              {disputeStats.rejected} rejected
            </span>
          </div>
          
          {disputeStats.approved >= requiresApprovals && (
            <div className="mt-2 text-xs text-red-600 font-medium">
              ⚠️ This benefit will be automatically removed
            </div>
          )}
        </div>
      )}

      {/* User's Dispute Status */}
      {hasActiveUserDispute && (
        <div className={`border rounded-lg p-3 ${
          userDispute.status === 'approved' ? 'bg-green-50 border-green-200' :
          userDispute.status === 'rejected' ? 'bg-red-50 border-red-200' :
          userDispute.status === 'cancelled' ? 'bg-gray-50 border-gray-200' :
          'bg-blue-50 border-blue-200'
        }`}>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-1">
            <div className="flex items-center gap-2">
              {userDispute.status === 'approved' && <CheckCircle className="w-4 h-4 text-green-600" />}
              {userDispute.status === 'rejected' && <XCircle className="w-4 h-4 text-red-600" />}
              {userDispute.status === 'cancelled' && <XCircle className="w-4 h-4 text-gray-600" />}
              {userDispute.status === 'pending' && <Clock className="w-4 h-4 text-blue-600" />}
              <span className="text-sm font-medium capitalize text-gray-900">
                Your dispute: {userDispute.status}
              </span>
            </div>
            {userDispute.status === 'pending' && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleCancelDispute}
                disabled={isCancelling}
                className="text-red-600 border-red-300 hover:bg-red-50 text-xs px-2 py-1 h-auto self-start sm:self-auto"
              >
                {isCancelling ? 'Cancelling...' : 'Cancel'}
              </Button>
            )}
          </div>
          <p className="text-xs text-gray-600 mb-1 break-words">
            Reason: {userDispute.reason}
          </p>
          {userDispute.admin_comment && (
            <p className="text-xs text-gray-500 break-words">
              Admin comment: {userDispute.admin_comment}
            </p>
          )}
        </div>
      )}

      {/* Dispute Action Button */}
      {canDispute && !hasActiveUserDispute && (
        <div className="space-y-2">
          {!showDisputeForm ? (
            <Button
              size="sm"
              variant="destructive"
              onClick={() => setShowDisputeForm(true)}
              className="flex items-center gap-2"
            >
              <Flag className="w-3 h-3" />
              Request Removal
            </Button>
          ) : (
            <div className="space-y-2">
              <textarea
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder={`Why should "${benefitName}" be removed from ${companyName}? Please provide a detailed reason.`}
                className="w-full p-2 text-sm border border-gray-300 rounded-md resize-none"
                rows={3}
                maxLength={500}
              />
              <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
                <Button
                  size="sm"
                  onClick={handleSubmitDispute}
                  disabled={isSubmitting || !reason.trim()}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Dispute'}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    setShowDisputeForm(false)
                    setReason('')
                  }}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              </div>
              <p className="text-xs text-gray-500 break-words">
                Two approved disputes from company employees are required to remove a verified benefit.
              </p>
            </div>
          )}
        </div>
      )}

      {!canDispute && !hasActiveUserDispute && !hasActiveDisputes && (
        <p className="text-xs text-gray-500 break-words">
          Only employees from {companyName} can request removal of verified benefits.
        </p>
      )}
    </div>
  )
}
