apiVersion: apps/v1
kind: Deployment
metadata:
  name: workwell
  namespace: workwell
spec:
  replicas: 3
  selector:
    matchLabels:
      app: workwell
  template:
    metadata:
      labels:
        app: workwell
    spec:
      containers:
      - name: workwell
        image: your-registry/workwell:latest  # Replace with your actual image
        ports:
        - containerPort: 3000
        envFrom:
        - configMapRef:
            name: workwell-config
        - secretRef:
            name: workwell-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
      securityContext:
        fsGroup: 1001
---
apiVersion: v1
kind: Service
metadata:
  name: workwell
  namespace: workwell
spec:
  selector:
    app: workwell
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: workwell-ingress
  namespace: workwell
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    cert-manager.io/cluster-issuer: "letsencrypt-prod"  # Optional: for SSL certificates
spec:
  tls:
  - hosts:
    - your-domain.com  # Replace with your actual domain
    secretName: workwell-tls
  rules:
  - host: your-domain.com  # Replace with your actual domain
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: workwell
            port:
              number: 80
