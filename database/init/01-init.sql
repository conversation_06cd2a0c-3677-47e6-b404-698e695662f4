-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Companies table
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255) NOT NULL,
    size VARCHAR(50) CHECK (size IN ('startup', 'small', 'medium', 'large', 'enterprise')) NOT NULL,
    industry VARCHAR(255) NOT NULL,
    description TEXT,
    domain VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    career_url VARCHAR(500)
);

-- Benefit categories table
CREATE TABLE benefit_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_system BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Benefits table
CREATE TABLE benefits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    category VARCHAR(50) CHECK (category IN ('health', 'time_off', 'financial', 'development', 'wellness', 'work_life', 'other')) NOT NULL,
    icon VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    description TEXT,
    category_id UUID NOT NULL REFERENCES benefit_categories(id)
);

-- Company benefits junction table
CREATE TABLE company_benefits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    benefit_id UUID NOT NULL REFERENCES benefits(id) ON DELETE CASCADE,
    added_by VARCHAR(255), -- User ID from auth system
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, benefit_id)
);

-- Company users table (for company representatives)
CREATE TABLE company_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, email)
);

-- Benefit verifications table
CREATE TABLE benefit_verifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_benefit_id UUID NOT NULL REFERENCES company_benefits(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL, -- User ID from auth system
    status VARCHAR(50) CHECK (status IN ('confirmed', 'disputed')) NOT NULL,
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Saved companies table
CREATE TABLE saved_companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, company_id)
);

-- User benefit rankings table
CREATE TABLE user_benefit_rankings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    benefit_id UUID NOT NULL REFERENCES benefits(id) ON DELETE CASCADE,
    ranking INTEGER NOT NULL CHECK (ranking >= 1 AND ranking <= 10), -- 1 = most important, 10 = least important
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, benefit_id)
);

-- Company verification tokens table
CREATE TABLE company_verification_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Users table for local authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL UNIQUE,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('user', 'admin')),
    payment_status VARCHAR(50) DEFAULT 'free' CHECK (payment_status IN ('free', 'paying')),
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL
);

-- Sessions table for local authentication
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);



-- Magic link tokens table
CREATE TABLE magic_link_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Magic link rate limits table
CREATE TABLE magic_link_rate_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL,
    attempts INTEGER DEFAULT 1,
    window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);



-- Missing company reports table
CREATE TABLE missing_company_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_email VARCHAR(255) NOT NULL,
    email_domain VARCHAR(255) NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    status VARCHAR(50) CHECK (status IN ('pending', 'reviewed', 'added', 'rejected')) DEFAULT 'pending',
    admin_notes TEXT,
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Activity log table
CREATE TABLE activity_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(100) NOT NULL,
    event_description TEXT NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL,
    benefit_id UUID REFERENCES benefits(id) ON DELETE SET NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Migration log table
CREATE TABLE migration_log (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX idx_companies_location ON companies(location);
CREATE INDEX idx_companies_size ON companies(size);
CREATE INDEX idx_companies_industry ON companies(industry);
CREATE INDEX idx_companies_domain ON companies(domain);

CREATE INDEX idx_benefits_category ON benefits(category);
CREATE INDEX idx_benefits_category_id ON benefits(category_id);
CREATE INDEX idx_benefits_name ON benefits(name);

CREATE INDEX idx_benefit_categories_name ON benefit_categories(name);
CREATE INDEX idx_benefit_categories_sort_order ON benefit_categories(sort_order);
CREATE INDEX idx_benefit_categories_is_active ON benefit_categories(is_active);

CREATE INDEX idx_company_benefits_company_id ON company_benefits(company_id);
CREATE INDEX idx_company_benefits_benefit_id ON company_benefits(benefit_id);
CREATE INDEX idx_company_benefits_verified ON company_benefits(is_verified);

CREATE INDEX idx_company_users_company_id ON company_users(company_id);
CREATE INDEX idx_company_users_email ON company_users(email);

CREATE INDEX idx_benefit_verifications_company_benefit_id ON benefit_verifications(company_benefit_id);
CREATE INDEX idx_benefit_verifications_user_id ON benefit_verifications(user_id);
CREATE INDEX idx_benefit_verifications_status ON benefit_verifications(status);

CREATE INDEX idx_saved_companies_user_id ON saved_companies(user_id);
CREATE INDEX idx_saved_companies_company_id ON saved_companies(company_id);

CREATE INDEX idx_user_benefit_rankings_user_id ON user_benefit_rankings(user_id);
CREATE INDEX idx_user_benefit_rankings_benefit_id ON user_benefit_rankings(benefit_id);
CREATE INDEX idx_user_benefit_rankings_ranking ON user_benefit_rankings(ranking);
CREATE INDEX idx_user_benefit_rankings_updated_at ON user_benefit_rankings(updated_at);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_company_id ON users(company_id);
CREATE INDEX idx_users_payment_status ON users(payment_status);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);



CREATE INDEX idx_magic_link_tokens_email ON magic_link_tokens(email);
CREATE INDEX idx_magic_link_tokens_token ON magic_link_tokens(token);
CREATE INDEX idx_magic_link_rate_limits_email ON magic_link_rate_limits(email);
CREATE INDEX idx_magic_link_rate_limits_window_start ON magic_link_rate_limits(window_start);

CREATE INDEX idx_company_verification_tokens_company_id ON company_verification_tokens(company_id);
CREATE INDEX idx_company_verification_tokens_user_id ON company_verification_tokens(user_id);
CREATE INDEX idx_company_verification_tokens_token ON company_verification_tokens(token);

CREATE INDEX idx_missing_company_reports_email_domain ON missing_company_reports(email_domain);
CREATE INDEX idx_missing_company_reports_status ON missing_company_reports(status);

CREATE INDEX idx_activity_log_event_type ON activity_log(event_type);
CREATE INDEX idx_activity_log_user_id ON activity_log(user_id);
CREATE INDEX idx_activity_log_company_id ON activity_log(company_id);
CREATE INDEX idx_activity_log_created_at ON activity_log(created_at);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Clean up expired tokens function
CREATE OR REPLACE FUNCTION cleanup_expired_magic_links()
RETURNS void AS $$
BEGIN
    DELETE FROM magic_link_tokens WHERE expires_at < NOW();
    DELETE FROM magic_link_rate_limits WHERE window_start < NOW() - INTERVAL '1 hour';
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER update_companies_updated_at BEFORE UPDATE ON companies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();



CREATE TRIGGER update_benefit_categories_updated_at BEFORE UPDATE ON benefit_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_missing_company_reports_updated_at BEFORE UPDATE ON missing_company_reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_benefit_rankings_updated_at BEFORE UPDATE ON user_benefit_rankings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default system benefit categories
INSERT INTO benefit_categories (name, display_name, description, icon, sort_order, is_system) VALUES
('health', 'Health & Medical', 'Health insurance, medical benefits, and healthcare-related perks', '🏥', 1, true),
('time_off', 'Time Off', 'Vacation days, sick leave, parental leave, and other time-off benefits', '🏖️', 2, true),
('financial', 'Financial', 'Retirement plans, stock options, bonuses, and financial benefits', '💰', 3, true),
('development', 'Development', 'Learning budgets, training programs, conference attendance, and professional development', '📚', 4, true),
('wellness', 'Wellness', 'Gym memberships, mental health support, wellness programs, and fitness benefits', '🧘', 5, true),
('work_life', 'Work-Life Balance', 'Flexible hours, remote work options, and work-life balance benefits', '⚖️', 6, true),
('other', 'Other Benefits', 'Miscellaneous benefits that don\'t fit into other categories', '🎁', 7, true);
