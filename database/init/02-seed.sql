-- Insert sample benefits with proper category_id references
INSERT INTO benefits (name, category, icon, description, category_id) VALUES
-- Health benefits
('Health Insurance', 'health', '🏥', NULL, (SELECT id FROM benefit_categories WHERE name = 'health')),
('Dental Insurance', 'health', '🦷', NULL, (SELECT id FROM benefit_categories WHERE name = 'health')),
('Vision Insurance', 'health', '👁️', NULL, (SELECT id FROM benefit_categories WHERE name = 'health')),
('Mental Health Support', 'health', '🧠', NULL, (SELECT id FROM benefit_categories WHERE name = 'health')),

-- Wellness benefits
('Gym Membership', 'wellness', '💪', NULL, (SELECT id FROM benefit_categories WHERE name = 'wellness')),
('Wellsport', 'wellness', '🏃', NULL, (SELECT id FROM benefit_categories WHERE name = 'wellness')),

-- Work-life balance benefits
('Flexible Working Hours', 'work_life', '⏰', NULL, (SELECT id FROM benefit_categories WHERE name = 'work_life')),
('Remote Work', 'work_life', '🏠', NULL, (SELECT id FROM benefit_categories WHERE name = 'work_life')),

-- Time off benefits
('Sabbatical Leave', 'time_off', '🌴', NULL, (SELECT id FROM benefit_categories WHERE name = 'time_off')),
('Unlimited PTO', 'time_off', '🏖️', NULL, (SELECT id FROM benefit_categories WHERE name = 'time_off')),
('Parental Leave', 'time_off', '👶', NULL, (SELECT id FROM benefit_categories WHERE name = 'time_off')),

-- Financial benefits
('Stock Options', 'financial', '📈', NULL, (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Retirement Plan', 'financial', '💰', NULL, (SELECT id FROM benefit_categories WHERE name = 'financial')),

-- Development benefits
('Learning Budget', 'development', '📚', NULL, (SELECT id FROM benefit_categories WHERE name = 'development')),
('Conference Attendance', 'development', '🎤', NULL, (SELECT id FROM benefit_categories WHERE name = 'development')),

-- Other benefits
('Free Lunch', 'other', '🍽️', NULL, (SELECT id FROM benefit_categories WHERE name = 'other')),
('Company Car', 'other', '🚗', NULL, (SELECT id FROM benefit_categories WHERE name = 'other')),
('Bike to Work Scheme', 'other', '🚲', NULL, (SELECT id FROM benefit_categories WHERE name = 'other')),
('Pet-Friendly Office', 'other', '🐕', NULL, (SELECT id FROM benefit_categories WHERE name = 'other')),
('Childcare Support', 'other', '👨‍👩‍👧‍👦', NULL, (SELECT id FROM benefit_categories WHERE name = 'other'));

-- Insert sample companies
INSERT INTO companies (name, location, size, industry, description, domain, career_url) VALUES
('SAP', 'Walldorf, Germany', 'enterprise', 'Technology', 'Global leader in enterprise software solutions', 'sap.com', 'https://jobs.sap.com'),
('Deutsche Bank', 'Frankfurt, Germany', 'enterprise', 'Financial Services', 'Leading global investment bank and financial services company', 'db.com', 'https://careers.db.com'),
('Accenture', 'Dublin, Ireland', 'enterprise', 'Consulting', 'Global professional services company with leading capabilities in digital, cloud and security', 'accenture.com', 'https://www.accenture.com/careers'),
('PwC', 'London, UK', 'large', 'Consulting', 'One of the Big Four accounting firms providing audit, tax and consulting services', 'pwc.com', 'https://www.pwc.com/careers'),
('Commerzbank', 'Frankfurt, Germany', 'large', 'Financial Services', 'Major German commercial bank', 'commerzbank.de', 'https://karriere.commerzbank.de'),
('DZ Bank', 'Frankfurt, Germany', 'large', 'Financial Services', 'Central institution for cooperative banks in Germany', 'dzbank.de', 'https://karriere.dzbank.de'),
('Lufthansa', 'Cologne, Germany', 'enterprise', 'Aviation', 'German airline and aviation group', 'lufthansa.com', 'https://careers.lufthansagroup.com'),
('Siemens', 'Munich, Germany', 'enterprise', 'Technology', 'Global technology company focused on industry, infrastructure, transport, and healthcare', 'siemens.com', 'https://jobs.siemens.com'),
('TechStart GmbH', 'Berlin, Germany', 'startup', 'Technology', 'Innovative startup focusing on AI and machine learning solutions', 'techstart.de', NULL),
('FinanceFlow', 'Amsterdam, Netherlands', 'medium', 'Financial Services', 'Modern fintech company providing digital banking solutions', 'financeflow.com', NULL);

-- Insert sample company benefits (linking companies with benefits)
INSERT INTO company_benefits (company_id, benefit_id, is_verified) 
SELECT 
    c.id,
    b.id,
    true
FROM companies c
CROSS JOIN benefits b
WHERE 
    (c.name = 'SAP' AND b.name IN ('Health Insurance', 'Wellsport', 'Flexible Working Hours', 'Remote Work', 'Learning Budget', 'Stock Options')) OR
    (c.name = 'Deutsche Bank' AND b.name IN ('Health Insurance', 'Wellsport', 'Retirement Plan', 'Stock Options', 'Gym Membership', 'Free Lunch')) OR
    (c.name = 'Accenture' AND b.name IN ('Health Insurance', 'Wellsport', 'Learning Budget', 'Conference Attendance', 'Flexible Working Hours', 'Mental Health Support')) OR
    (c.name = 'PwC' AND b.name IN ('Health Insurance', 'Wellsport', 'Learning Budget', 'Flexible Working Hours', 'Parental Leave', 'Bike to Work Scheme')) OR
    (c.name = 'Commerzbank' AND b.name IN ('Health Insurance', 'Retirement Plan', 'Stock Options', 'Gym Membership', 'Parental Leave')) OR
    (c.name = 'DZ Bank' AND b.name IN ('Health Insurance', 'Retirement Plan', 'Flexible Working Hours', 'Learning Budget')) OR
    (c.name = 'Lufthansa' AND b.name IN ('Health Insurance', 'Company Car', 'Parental Leave', 'Retirement Plan', 'Free Lunch')) OR
    (c.name = 'Siemens' AND b.name IN ('Health Insurance', 'Learning Budget', 'Stock Options', 'Flexible Working Hours', 'Gym Membership', 'Mental Health Support')) OR
    (c.name = 'TechStart GmbH' AND b.name IN ('Health Insurance', 'Remote Work', 'Flexible Working Hours', 'Stock Options', 'Free Lunch')) OR
    (c.name = 'FinanceFlow' AND b.name IN ('Health Insurance', 'Wellsport', 'Remote Work', 'Learning Budget', 'Mental Health Support'));

-- Insert some unverified benefits for testing
INSERT INTO company_benefits (company_id, benefit_id, is_verified) 
SELECT 
    c.id,
    b.id,
    false
FROM companies c
CROSS JOIN benefits b
WHERE 
    (c.name = 'SAP' AND b.name IN ('Sabbatical Leave', 'Pet-Friendly Office')) OR
    (c.name = 'Deutsche Bank' AND b.name IN ('Unlimited PTO', 'Childcare Support')) OR
    (c.name = 'TechStart GmbH' AND b.name IN ('Sabbatical Leave', 'Unlimited PTO', 'Pet-Friendly Office')) OR
    (c.name = 'FinanceFlow' AND b.name IN ('Bike to Work Scheme', 'Conference Attendance'));

-- Insert sample users for testing
INSERT INTO users (email, first_name, last_name, email_verified, role, payment_status, company_id) VALUES
('<EMAIL>', 'Admin', 'User', true, 'admin', 'free', NULL),
('<EMAIL>', 'John', 'Doe', true, 'user', 'paying', (SELECT id FROM companies WHERE name = 'SAP')),
('<EMAIL>', 'Jane', 'Smith', true, 'user', 'free', (SELECT id FROM companies WHERE name = 'Deutsche Bank')),
('<EMAIL>', 'Mike', 'Wilson', true, 'user', 'paying', (SELECT id FROM companies WHERE name = 'Accenture')),
('<EMAIL>', 'Sarah', 'Johnson', true, 'user', 'free', (SELECT id FROM companies WHERE name = 'TechStart GmbH')),
('<EMAIL>', 'Test', 'User', true, 'user', 'free', NULL);

-- Insert company users (representatives who can manage company benefits)
INSERT INTO company_users (company_id, email, is_verified)
SELECT c.id, u.email, true
FROM companies c
JOIN users u ON u.company_id = c.id
WHERE u.email NOT LIKE 'admin@%' AND u.email NOT LIKE 'test@%';
